﻿using FluentValidation.TestHelper;
using KornFerry.Services.Service.ProductsApi;
using KornFerry.Services.Service.ProductsApi.Models;
using Moq;
using System.Collections.Generic;
using TalentQ.Integrations.Hub;
using TalentQ.Integrations.Hub.KFOne.Project.Models;
using Xunit;
using Xunit.Extensions;

namespace TalentQ.Integrations.Tests.Unit.Hub.KFOne.Project.Models
{
    public class KFOneProjectCreateUpdateValidatorTests
    {
        Mock<IProductsApiService> productsAPIService = new Mock<IProductsApiService>();
        Mock<IClientReportService> clientReportService = new Mock<IClientReportService>();
        KFOneProjectCreateUpdateValidator validator;

        public KFOneProjectCreateUpdateValidatorTests()
        {
            validator = new KFOneProjectCreateUpdateValidator(productsAPIService.Object, clientReportService.Object);
        }

        [Fact]
        public void ExternalClientId_ShouldError_WhenZero()
        {
            validator.ShouldHaveValidationErrorFor(m => m.ExternalClientId, 0);
        }

        [Fact]
        public void ExternalClientId_ShouldError_WhenLessThanZero()
        {
            validator.ShouldHaveValidationErrorFor(m => m.ExternalClientId, -4);
        }

        [Fact]
        public void ExternalClient_ShouldNotError_WhenGreaterThanZero()
        {
            validator.ShouldNotHaveValidationErrorFor(m => m.ExternalClientId, 14193);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        public void ProjectName_ShouldError_WhenIsMissing(string projectName)
        {
            validator.ShouldHaveValidationErrorFor(m => m.Name, projectName);
        }

        [Fact]
        public void ProjectName_ShouldError_WhenIsLongerThan255()
        {
            var projectName = new string('a', 256);
            validator.ShouldHaveValidationErrorFor(m => m.Name, projectName);
        }

        [Theory]
        [InlineData(">projectName")]
        [InlineData("<projectName")]
        [InlineData("\"projectName")]
        [InlineData("%projectName")]
        [InlineData(";projectName")]
        [InlineData("&projectName")]
        [InlineData("#projectName")]
        [InlineData("#pro>jectName")]
        [InlineData("Project>Name")]
        [InlineData("Project<Name")]
        [InlineData("\"projectName")]
        [InlineData("Project%Name")]
        [InlineData("Project;Name")]
        [InlineData("Project&Name")]
        [InlineData("Project#Name")]
        public void ProjectName_ShouldError_WhenContainsInValidCharacters(string projectName)
        {
            validator.ShouldHaveValidationErrorFor(m => m.Name, projectName);
        }

        [Theory]
        [InlineData("Valid Project Name")]
        [InlineData("A")]
        [InlineData("Simple")]
        [InlineData("A simple but longer name. A simple but longer name. A simple but longer name. A simple but longer name. A simple but longer name. A simple but longer name.")]
        [InlineData("Some valid !£$^*[]{}'@:.,`¬ special characters")]
        public void ProjectName_ShouldNotError_WhenIsValid(string projectName)
        {
            validator.ShouldNotHaveValidationErrorFor(m => m.Name, projectName);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        public void Platform_ShouldError_WhenIsMissing(string platform)
        {
            validator.ShouldHaveValidationErrorFor(m => m.Platform, platform);
        }

        [Fact]
        public void Platform_ShouldError_WhenIsLongerThan25()
        {
            var platform = new string('a', 26);
            validator.ShouldHaveValidationErrorFor(m => m.Platform, platform);
        }

        [Theory]
        [InlineData("Some Platform")]
        [InlineData("A")]
        [InlineData("Simple")]
        [InlineData("KFOne-Dev")]
        [InlineData("A simple but longer name")]
        [InlineData("Some valid !£$^*[]{}'@:.")]
        public void Platform_ShouldNotError_WhenIsValid(string platform)
        {
            validator.ShouldNotHaveValidationErrorFor(m => m.Platform, platform);
        }

        [Theory]
        [InlineData("")]
        [InlineData("     ")]
        [InlineData(null)]
        public void Location_ShouldError_WhenEmpty(string location)
        {
            validator.ShouldHaveValidationErrorFor(m => m.Location, location);
        }

        [Fact]
        public void Location_ShouldError_WhenLocationIsNotFound()
        {
            productsAPIService.Setup(p => p.GetRegionalNormsAsync(It.IsAny<int>())).ReturnsAsync(new RegionalNorms());

            validator.ShouldHaveValidationErrorFor(m => m.Location, "NotFound");
        }

        [Fact]
        public void Location_ShouldNotError_WhenLocationIsFound()
        {
            productsAPIService.Setup(p => p.GetRegionalNormsAsync(It.IsAny<int>())).ReturnsAsync(new RegionalNorms()
            {
                Data = new RegionalNormData
                {
                    Locations = new RegionalNormLocation[] {
                        new RegionalNormLocation
                        {
                            CountryCode = "GB"
                        }
                    }
                }
            });

            validator.ShouldNotHaveValidationErrorFor(m => m.Location, "GB");
        }

        [Theory]
        [InlineData("")]
        [InlineData("     ")]
        [InlineData(null)]
        public void Norm_ShouldError_WhenEmptyAndSuccessProfileIsDefined(string norm)
        {
            var model = new KFOneProjectCreateUpdate
            {
                Norm = norm,
                SuccessProfileId = 1
            };

            validator.ShouldHaveValidationErrorFor(m => m.Norm, model);
        }

        [Fact]
        public void Norm_ShouldError_WhenNormIsNotFoundAndSuccessProfileIsDefined()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Norm = "NotFound",
                SuccessProfileId = 1
            };

            productsAPIService.Setup(p => p.GetRegionalNormsAsync(It.IsAny<int>())).ReturnsAsync(new RegionalNorms());

            validator.ShouldHaveValidationErrorFor(m => m.Norm, model);
        }

        [Theory]
        [InlineData("")]
        [InlineData("     ")]
        [InlineData(null)]
        public void Norm_ShouldNotError_WhenEmptyAndSuccessProfileIsNotDefined(string norm)
        {
            var model = new KFOneProjectCreateUpdate
            {
                Norm = norm,
                SuccessProfileId = null
            };

            validator.ShouldNotHaveValidationErrorFor(m => m.Norm, model);
        }

        [Fact]
        public void Norm_ShouldNotError_WhenNormIsNotFoundAndSuccessProfileIsNotDefined()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Norm = "NotFound",
                SuccessProfileId = null
            };

            productsAPIService.Setup(p => p.GetRegionalNormsAsync(It.IsAny<int>())).ReturnsAsync(new RegionalNorms());

            validator.ShouldNotHaveValidationErrorFor(m => m.Norm, model);
        }

        [Fact]
        public void Norm_ShouldNotError_WhenNormIsFound()
        {
            productsAPIService.Setup(p => p.GetRegionalNormsAsync(It.IsAny<int>())).ReturnsAsync(new RegionalNorms()
            {
                Data = new RegionalNormData
                {
                    RegionalNorms = new RegionalNorm[] {
                        new RegionalNorm
                        {
                            Country = "GLOBAL"
                        }
                    }
                }
            });

            validator.ShouldNotHaveValidationErrorFor(m => m.Norm, "Global");
        }

        [Fact]
        public void SuccessProfileId_ShouldNotError_WhenNull()
        {
            validator.ShouldNotHaveValidationErrorFor(m => m.SuccessProfileId, (int?)null);
        }

        [Fact]
        public void SuccessProfileId_ShouldError_WhenZero()
        {
            validator.ShouldHaveValidationErrorFor(m => m.SuccessProfileId, 0);
        }

        [Fact]
        public void SuccessProfileId_ShouldError_WhenLessThanZero()
        {
            validator.ShouldHaveValidationErrorFor(m => m.SuccessProfileId, -4);
        }

        [Fact]
        public void SuccessProfile_ShouldNotError_WhenGreaterThanZero()
        {
            validator.ShouldNotHaveValidationErrorFor(m => m.SuccessProfileId, 546212);
        }

        [Fact]
        public void SuccessProfile_ShouldError_WhenTheTechnicalSkillsIsMeasuredAndSuccessProfileIsNull()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Assessments = new Integrations.Hub.KFOne.Project.Models.ProjectAssessments
                {
                    TechnicalSkillsInventory = new MeasuredProjectAssessment
                    {
                        Measure = true
                    }
                },
                SuccessProfileId = null
            };

            validator.ShouldHaveValidationErrorFor(m => m.SuccessProfileId, model);
        }

        [Fact]
        public void SuccessProfile_ShouldError_WhenTheTechnicalSkillsIsMeasuredAndSuccessProfileIsZero()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Assessments = new Integrations.Hub.KFOne.Project.Models.ProjectAssessments
                {
                    TechnicalSkillsInventory = new MeasuredProjectAssessment
                    {
                        Measure = true
                    }
                },
                SuccessProfileId = 0
            };

            validator.ShouldHaveValidationErrorFor(m => m.SuccessProfileId, model);
        }

        [Fact]
        public void SuccessProfile_ShouldNotError_WhenTheTechnicalSkillsIsMeasuredAndSuccessProfileIdHasValue()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Assessments = new Integrations.Hub.KFOne.Project.Models.ProjectAssessments
                {
                    TechnicalSkillsInventory = new MeasuredProjectAssessment
                    {
                        Measure = true
                    }
                },
                SuccessProfileId = 32151
            };

            validator.ShouldNotHaveValidationErrorFor(m => m.SuccessProfileId, model);
        }

        [Fact]
        public void Assessments_ShouldError_WhenNull()
        {
            validator.ShouldHaveValidationErrorFor(m => m.Assessments, (Integrations.Hub.KFOne.Project.Models.ProjectAssessments)null);
        }

        [Fact]
        public void Assessments_ShouldNotError_WhenAnPopulated()
        {
            validator.ShouldNotHaveValidationErrorFor(m => m.Assessments, new Integrations.Hub.KFOne.Project.Models.ProjectAssessments());
        }

        [Fact]
        public void Assessments_ShouldHaveChildValidator()
        {
            validator.ShouldHaveChildValidator(m => m.Assessments, typeof(ProjectAssessmentsValidator));
        }

        [Fact]
        public void PotentialLevels_ShouldHaveChildValidator()
        {
            validator.ShouldHaveChildValidator(m => m.PotentialLevels, typeof(PotentialLevelsValidator));
        }

        [Fact]
        public void PreferenceAssessment_ShouldError_WithPotentialLevelsAndNull()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Assessments = new Integrations.Hub.KFOne.Project.Models.ProjectAssessments
                {
                    Preferences = null
                },
                PotentialLevels = new PotentialLevels
                {
                    TargetLevel = "POT_LVL_01",
                    CurrentLevel = "POT_LVL_02"
                }
            };

            validator.ShouldHaveValidationErrorFor(m => m.Assessments.Preferences, model);
        }

        [Fact]
        public void PreferenceAssessment_ShouldError_WithPotentialLevelsAndNotMeasured()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Assessments = new Integrations.Hub.KFOne.Project.Models.ProjectAssessments
                {
                    Preferences = new MeasuredProjectAssessment
                    {
                        Measure = false
                    }
                },
                PotentialLevels = new PotentialLevels
                {
                    TargetLevel = "POT_LVL_01",
                    CurrentLevel = "POT_LVL_02"
                }
            };

            validator.ShouldHaveValidationErrorFor(m => m.Assessments.Preferences, model);
        }

        [Fact]
        public void PreferenceAssessment_ShouldNotError_WithPotentialLevelsAndMeasured()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Assessments = new Integrations.Hub.KFOne.Project.Models.ProjectAssessments
                {
                    Preferences = new MeasuredProjectAssessment
                    {
                        Measure = true
                    }
                },
                PotentialLevels = new PotentialLevels
                {
                    TargetLevel = "POT_LVL_01",
                    CurrentLevel = "POT_LVL_02"
                }
            };

            validator.ShouldNotHaveValidationErrorFor(m => m.Assessments.Preferences, model);
        }

        [Fact]
        public void TraitsAssessment_ShouldError_WithPotentialLevelsAndNull()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Assessments = new Integrations.Hub.KFOne.Project.Models.ProjectAssessments
                {
                    Traits = null
                },
                PotentialLevels = new PotentialLevels
                {
                    TargetLevel = "POT_LVL_01",
                    CurrentLevel = "POT_LVL_02"
                }
            };

            validator.ShouldHaveValidationErrorFor(m => m.Assessments.Traits, model);
        }


        [Fact]
        public void TraitsAssessment_ShouldError_WithPotentialLevelsAndNotMeasured()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Assessments = new Integrations.Hub.KFOne.Project.Models.ProjectAssessments
                {
                    Traits = new MeasuredProjectAssessment
                    {
                        Measure = false
                    }
                },
                PotentialLevels = new PotentialLevels
                {
                    TargetLevel = "POT_LVL_01",
                    CurrentLevel = "POT_LVL_02"
                }
            };

            validator.ShouldHaveValidationErrorFor(m => m.Assessments.Traits, model);
        }

        [Fact]
        public void TraitsAssessment_ShouldNotError_WithPotentialLevelsAndMeasured()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Assessments = new Integrations.Hub.KFOne.Project.Models.ProjectAssessments
                {
                    Traits = new MeasuredProjectAssessment
                    {
                        Measure = true
                    }
                },
                PotentialLevels = new PotentialLevels
                {
                    TargetLevel = "POT_LVL_01",
                    CurrentLevel = "POT_LVL_02"
                }
            };

            validator.ShouldNotHaveValidationErrorFor(m => m.Assessments.Traits, model);
        }

        [Fact]
        public void DriversAssessment_ShouldError_WithPotentialLevelsAndNull()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Assessments = new Integrations.Hub.KFOne.Project.Models.ProjectAssessments
                {
                    Drivers = null
                },
                PotentialLevels = new PotentialLevels
                {
                    TargetLevel = "POT_LVL_01",
                    CurrentLevel = "POT_LVL_02"
                }
            };

            validator.ShouldHaveValidationErrorFor(m => m.Assessments.Drivers, model);
        }

        [Fact]
        public void DriversAssessment_ShouldError_WithPotentialLevelsAndNotMeasured()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Assessments = new Integrations.Hub.KFOne.Project.Models.ProjectAssessments
                {
                    Drivers = new MeasuredProjectAssessment
                    {
                        Measure = false
                    }
                },
                PotentialLevels = new PotentialLevels
                {
                    TargetLevel = "POT_LVL_01",
                    CurrentLevel = "POT_LVL_02"
                }
            };

            validator.ShouldHaveValidationErrorFor(m => m.Assessments.Drivers, model);
        }

        [Fact]
        public void DriversAssessment_ShouldNotError_WithPotentialLevelsAndMeasured()
        {
            var model = new KFOneProjectCreateUpdate
            {
                Assessments = new Integrations.Hub.KFOne.Project.Models.ProjectAssessments
                {
                    Drivers = new MeasuredProjectAssessment
                    {
                        Measure = true
                    }
                },
                PotentialLevels = new PotentialLevels
                {
                    TargetLevel = "POT_LVL_01",
                    CurrentLevel = "POT_LVL_02"
                }
            };

            validator.ShouldNotHaveValidationErrorFor(m => m.Assessments.Drivers, model);
        }

        [Fact]
        public void DfrReports_ShouldError_WhenReportNotAvailable()
        {
            clientReportService.Setup(s => s.CheckforValidParticipantReport(null, It.IsAny<IEnumerable<string>>())).Returns(false);
            validator.ShouldHaveValidationErrorFor(m => m.DfrReports, new[] { "SomeReport" });
        }

        [Fact]
        public void DfrReports_ShouldNotError_WhenReportIsAvailable()
        {
            clientReportService.Setup(s => s.CheckforValidParticipantReport(null, It.IsAny<IEnumerable<string>>())).Returns(true);
            validator.ShouldNotHaveValidationErrorFor(m => m.DfrReports, new[] { "SomeReport" });
        }

        [Fact]
        public void DfrReports_ShouldNotError_WhenNoReportsRequested()
        {
            clientReportService.Setup(s => s.CheckforValidParticipantReport(null, It.IsAny<IEnumerable<string>>())).Returns(false);
            validator.ShouldNotHaveValidationErrorFor(m => m.DfrReports, new string[] { });
        }
    }
}
