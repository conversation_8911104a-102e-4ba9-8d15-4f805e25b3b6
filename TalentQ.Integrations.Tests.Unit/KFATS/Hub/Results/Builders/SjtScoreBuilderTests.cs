﻿using FluentAssertions;
using KornFerry.Services.Content.Sjt;
using KornFerry.Services.Content.TextLookup.Sjt;
using KornFerry.Services.Extracts.Service;
using KornFerry.Services.Scoring.Shared.Storage;
using KornFerry.Services.Scoring.Shared.Storage.Models;
using Moq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TalentQ.Integrations.KFATS.Hub.Results;
using TalentQ.Integrations.KFATS.Hub.Results.Configuration;
using TQLogic.Assessments.Player;
using TQLogic.Authentication;
using TQLogic.Projects;
using TQLogic.Projects.Hub;
using Xunit;
using Xunit.Extensions;
using static KornFerry.Services.Extracts.Service.ScoreResult;

namespace TalentQ.Integrations.Tests.Unit.KFATS.Hub.Results.Builders
{
    public class SjtScoreBuilderTests
    {
        private readonly Mock<IScoreExtract> scoreExtract = new Mock<IScoreExtract>(MockBehavior.Strict);
        private readonly Mock<ISjtTextLookup> textLookup = new Mock<ISjtTextLookup>(MockBehavior.Strict);
        private readonly Mock<ICredentialRepository> credentialRepository = new Mock<ICredentialRepository>();

        private const int candidateId = -7802;
        private const int projectId = 4556;
        private const int playerTestId = 51;

        public SjtScoreBuilderTests()
        {
            credentialRepository.Setup(c => c.IsUserNameUnique(It.IsAny<string>())).Returns(true);
        }

        private ProjectHub CreateProjectHub() =>
            new ProjectHub
            {
                ProjectId = projectId,
                AssessmentsJson = JsonConvert.SerializeObject(new ProjectHubAssessments
                {
                    SJT = new SJT
                    {
                        AssessmentId = playerTestId,
                        Measure = true
                    }
                })
            };

        private SjtScoreBuilder CreateBuilder(ProjectHub project = null, AssessmentResultBuilderConfiguration configuration = null)
            => new SjtScoreBuilder(candidateId, project ?? CreateProjectHub(), configuration, scoreExtract.Object, textLookup.Object);

        private CandidateExtractResult CreateExtractResult(IDictionary<ScoreDetails, ScoreResult> scores) =>
            new CandidateExtractResult
            {
                Candidates = new List<CandidateResult>
                    {
                        new CandidateResult
                        {
                            Projects = new List<ProjectResult>
                            {
                                new ProjectResult
                                {
                                    Scores = scores
                                }
                            }
                        }
                    }
            };

        [Fact]
        public void Constructor_TextLookupNull_ArgumentNullExceptionThrown()
        {
            Action act = () => new SjtScoreBuilder(candidateId, CreateProjectHub(), new AssessmentResultBuilderConfiguration(), scoreExtract.Object, null);
            act.Should().Throw<ArgumentNullException>();
        }

        [Fact]
        public void BuildCaclulatorScores_And_BuildAssessmentResults_NoScoresFound_ExceptionThrown()
        {
            scoreExtract.Setup(s => s.GetScoresAsync(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(Task.FromResult(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>())));
            var builder = CreateBuilder();

            Action calcAct = () => builder.BuildCalculatorScores();
            calcAct.Should().Throw<Exception>();

            Action assAct = () => builder.BuildAssessmentResults();
            assAct.Should().Throw<Exception>();
        }

        [Fact]
        public void BuildCaclulatorScores_And_BuildAssessmentResults_NoScalesFound_ExceptionThrown()
        {
            scoreExtract.Setup(s => s.GetScoresAsync(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(Task.FromResult(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult> { [new ScoreDetails()] = new ScoreResult() })));
            textLookup.Setup(p => p.GetScales(It.IsAny<int>())).Returns(Enumerable.Empty<SjtScale>());

            var builder = CreateBuilder();

            Action calcAct = () => builder.BuildCalculatorScores();
            calcAct.Should().Throw<Exception>();

            Action assAct = () => builder.BuildAssessmentResults();
            assAct.Should().Throw<Exception>();
        }

        [Fact]
        public void BuildCalculatorScores_And_BuildAssessmentResults_NoOverallScoreFound_ExceptionThrown()
        {
            var scales = new List<SjtScale>
            {
                new SjtScale
                {
                    Type = PlayerScaleType.Competency
                }
            };

            scoreExtract.Setup(s => s.GetScoresAsync(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(Task.FromResult(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult> { [new ScoreDetails()] = new ScoreResult() })));
            textLookup.Setup(p => p.GetScales(It.IsAny<int>())).Returns(scales);

            var builder = CreateBuilder();

            Action calcAct = () => builder.BuildCalculatorScores();
            calcAct.Should().Throw<Exception>();

            Action assAct = () => builder.BuildAssessmentResults();
            assAct.Should().Throw<Exception>();
        }

        [Fact]
        public void BuildCalculatorScores_OverallScoreAvailable_2CalculatorScoresReturned()
        {
            const int overallScaleId = 10;
            const int overallFit = 5;
            const int overallPercentile = 80;
            var scales = new List<SjtScale>
            {
                new SjtScale
                {
                    Id = overallScaleId,
                    Type = PlayerScaleType.Assessment,
                    Display = true
                },
                new SjtScale
                {
                    Id = 14,
                    Type = PlayerScaleType.Competency,
                    Display = true
                }
            };

            var overallScoreResult = new ScoreResult();
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, overallScaleId.ToString()), new KfasScoreItem { Fit = overallFit, Percentile = overallPercentile });

            scoreExtract.Setup(s => s.GetScoresAsync(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(Task.FromResult(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                })));
            scoreExtract.Setup(s => s.GetScores(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                }));

            textLookup.Setup(p => p.GetScales(It.IsAny<int>())).Returns(scales);

            var builder = CreateBuilder();
            var scores = builder.BuildCalculatorScores();

            scores.Count().Should().Be(2, "2 scores expected");

            var sjtFit = scores.FirstOrDefault(s => s.ScoreType == Integrations.KFATS.Common.Results.CalculatorScoreType.Fit);
            sjtFit.Should().NotBeNull("expecting a fit score");
            sjtFit.Score.Should().Be(overallFit, "fit score");

            var sjtPercentile = scores.FirstOrDefault(s => s.ScoreType == Integrations.KFATS.Common.Results.CalculatorScoreType.Percentile);
            sjtPercentile.Should().NotBeNull("expecting a percentile score");
            sjtPercentile.Score.Should().Be(overallPercentile, "percentile score");
        }

        [Fact]
        public void BuildAssessmentResults_OverallScoreOnly_NoCompetencies_OverallScoreOnlyReturned()
        {
            const int overallScaleId = 10;
            const int overallFit = 5;
            const int overallPercentile = 80;
            const string playerTestName = "readyplayerone";
            var scales = new List<SjtScale>
            {
                new SjtScale
                {
                    Id = overallScaleId,
                    Type = PlayerScaleType.Assessment,
                    Display = true
                },
                new SjtScale
                {
                    Id = 14,
                    Type = PlayerScaleType.Competency,
                    Display = true
                }
            };

            var overallScoreResult = new ScoreResult();
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, overallScaleId.ToString()), new KfasScoreItem { Fit = overallFit, Percentile = overallPercentile });

            scoreExtract.Setup(s => s.GetScoresAsync(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(Task.FromResult(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                })));
            scoreExtract.Setup(s => s.GetScores(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                }));

            textLookup.Setup(p => p.GetScales(It.IsAny<int>())).Returns(scales);
            textLookup.Setup(t => t.GetName()).Returns(playerTestName);

            var builder = CreateBuilder();
            var scores = builder.BuildAssessmentResults();

            scores.Count().Should().Be(1, "one result expected");

            var result = scores.First();
            result.AssessmentDetails.Should().BeNullOrEmpty("no competency results");
            result.AssessmentOverall.Description.Should().Be(playerTestName);
            result.AssessmentOverall.FitScore.Should().Be(overallFit);
            result.AssessmentOverall.Percentile.Should().Be(overallPercentile);
            result.AssessmentOverall.Summary.Should().BeEmpty("no competency results");
        }

        [Fact]
        public void BuildAssessmentResults_OverallAndCompetencies()
        {
            const int overallScaleId = 10;
            const int scaleOneId = 1;
            const int scaleTwoId = 2;
            const int overallFit = 5;
            const int overallPercentile = 80;
            const string playerTestName = "readyplayerone";

            var scales = new List<SjtScale>
            {
                new SjtScale
                {
                    Id = overallScaleId,
                    Type = PlayerScaleType.Assessment,
                    Display = true
                },
                new SjtScale
                {
                    Id = scaleOneId,
                    Type = PlayerScaleType.Competency,
                    Name = "something",
                    Display = true
                },
                new SjtScale
                {
                    Id = scaleTwoId,
                    Type = PlayerScaleType.Competency,
                    Name = "something else",
                    Display = true
                }
            };

            var overallScoreResult = new ScoreResult();
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, overallScaleId.ToString()), new KfasScoreItem { Fit = overallFit, Percentile = overallPercentile });
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Competency, scaleOneId.ToString()), new KfasScoreItem { Fit = 2, Percentile = 45 });
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Competency, scaleTwoId.ToString()), new KfasScoreItem { Fit = 4, Percentile = 35 });

            scoreExtract.Setup(s => s.GetScoresAsync(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(Task.FromResult(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                })));
            scoreExtract.Setup(s => s.GetScores(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                }));

            textLookup.Setup(p => p.GetScales(It.IsAny<int>())).Returns(scales);
            textLookup.Setup(t => t.GetName()).Returns(playerTestName);

            var builder = CreateBuilder();
            var scores = builder.BuildAssessmentResults();

            scores.Count().Should().Be(1, "one result expected");

            var result = scores.First();
            result.AssessmentOverall.Description.Should().Be(playerTestName);
            result.AssessmentOverall.FitScore.Should().Be(overallFit);
            result.AssessmentOverall.Percentile.Should().Be(overallPercentile);
            result.AssessmentOverall.Summary.Should().NotBeNullOrEmpty("there are competency results");

            result.AssessmentDetails.Count.Should().Be(scales.Count(s => s.Type == PlayerScaleType.Competency));

            foreach (var scale in scales.Where(s => s.Type == PlayerScaleType.Competency))
            {
                var scaleResult = result.AssessmentDetails.FirstOrDefault(a => a.AssessmentDetail.Description == scales.First(s => s.Id == scale.Id).Name);
                scaleResult.Should().NotBeNull($"result for scaleId {scale.Id} with description '{scales.First(s => s.Id == scale.Id).Name} expected'");
                var expectedScore = overallScoreResult.GetScore(ScoreGroup.Competency, scale.Id.ToString());
                scaleResult.AssessmentDetail.FitScore.Should().Be(expectedScore.Fit);
                scaleResult.AssessmentDetail.Percentile.Should().Be(expectedScore.Percentile);
            }
        }

        /// <summary>
        /// Check the correct score is used in the summary field
        /// </summary>
        /// <param name="projectScore">The project setting</param>
        /// <param name="configScore">The result configuration setting</param>
        /// <param name="expectedScore">The score setting expected to be used by the builder</param>
        /// <param name="description">A description of the test case</param>
        [Theory]
        [InlineData(ScoreDisplayType.Fit, null, ScoreDisplayType.Fit, "no configuration, use project setting of fit")]
        [InlineData(ScoreDisplayType.Percentile, null, ScoreDisplayType.Percentile, "no configuration, use project setting of percentile")]
        [InlineData(ScoreDisplayType.Fit, ScoreDisplayType.Percentile, ScoreDisplayType.Percentile, "configuration of percentile overrides project setting of fit")]
        [InlineData(ScoreDisplayType.Percentile, ScoreDisplayType.Fit, ScoreDisplayType.Fit, "configuration of fit overrides project setting of percentile")]
        [InlineData(ScoreDisplayType.Fit, ScoreDisplayType.Fit, ScoreDisplayType.Fit, "configuration and project both use fit")]
        [InlineData(ScoreDisplayType.Percentile, ScoreDisplayType.Percentile, ScoreDisplayType.Percentile, "configuration and project both use percentile")]
        public void BuildAssessmentResults_SummaryText(ScoreDisplayType projectScore, ScoreDisplayType? configScore, ScoreDisplayType expectedScore, string description)
        {
            const int overallScaleId = 10;
            const int scaleOneId = 1;
            const int scaleTwoId = 2;
            const int scaleThreeId = 3;
            const int overallFit = 5;
            const int overallPercentile = 80;
            const string playerTestName = "readyplayerone";

            var scales = new List<SjtScale>
            {
                new SjtScale
                {
                    Id = overallScaleId,
                    Type = PlayerScaleType.Assessment,
                    Display = true
                },
                new SjtScale
                {
                    Id = scaleOneId,
                    Type = PlayerScaleType.Competency,
                    Name = "something",
                    Display = true
                },
                new SjtScale
                {
                    Id = scaleTwoId,
                    Type = PlayerScaleType.Competency,
                    Name = "something else",
                    Display = true
                },
                new SjtScale // this item should be ignored by the summary because it has no name
                {
                    Id = scaleThreeId,
                    Type = PlayerScaleType.Competency,
                    Name = "",
                    Display = true
                }
            };

            var overallScoreResult = new ScoreResult();
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, overallScaleId.ToString()), new KfasScoreItem { Fit = overallFit, Percentile = overallPercentile });
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Competency, scaleOneId.ToString()), new KfasScoreItem { Fit = 2, Percentile = 45 });
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Competency, scaleTwoId.ToString()), new KfasScoreItem { Fit = 4, Percentile = 35 });
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Competency, scaleThreeId.ToString()), new KfasScoreItem { Fit = 5, Percentile = 65 });

            string expectedSummary;
            if (expectedScore == ScoreDisplayType.Percentile)
            {
                expectedSummary = "something: 45, something else: 35";
            }
            else
            {
                expectedSummary = "something: 2, something else: 4";
            }

            scoreExtract.Setup(s => s.GetScoresAsync(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(Task.FromResult(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                })));
            scoreExtract.Setup(s => s.GetScores(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                }));

            textLookup.Setup(p => p.GetScales(It.IsAny<int>())).Returns(scales);
            textLookup.Setup(t => t.GetName()).Returns(playerTestName);

            var project = CreateProjectHub();
            project.ScoreDisplay = projectScore;

            var configuration = new AssessmentResultBuilderConfiguration
            {
                SummaryScore = configScore
            };

            var builder = CreateBuilder(project, configuration);
            var scores = builder.BuildAssessmentResults();

            var result = scores.First();
            result.AssessmentOverall.Summary.Should().Be(expectedSummary, description);
        }
    }
}
