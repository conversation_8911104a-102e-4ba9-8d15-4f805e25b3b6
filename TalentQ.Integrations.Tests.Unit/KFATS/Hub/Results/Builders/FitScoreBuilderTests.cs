﻿using FluentAssertions;
using KornFerry.Services.Content;
using KornFerry.Services.Extracts.Service;
using KornFerry.Services.Scoring.Shared.Storage;
using KornFerry.Services.Scoring.Shared.Storage.Models;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TalentQ.Integrations.KFATS.Common.Models;
using TalentQ.Integrations.KFATS.Common.Results;
using TalentQ.Integrations.KFATS.Hub.Results;
using TQCommon;
using TQLogic;
using TQLogic.Assessments;
using TQLogic.Authentication;
using TQLogic.Projects.Hub;
using Xunit;
using static KornFerry.Services.Extracts.Service.ScoreResult;

namespace TalentQ.Integrations.Tests.Unit.KFATS.Hub.Results.Builders
{
    public class FitScoreBuilderTests
    {
        // Use strict behaviour to check that only expected methods are called
        private readonly Mock<IScoreExtract> scoreExtract = new Mock<IScoreExtract>(MockBehavior.Strict);
        private readonly Mock<ICommonTextLookup> textLookup = new Mock<ICommonTextLookup>(MockBehavior.Strict);
        private readonly Mock<ICredentialRepository> credentialRepository = new Mock<ICredentialRepository>();

        private const int candidateId = -7802;
        private const int projectId = 4556;
        private const int externalClientId = 54452323;
        private const int successProfileId = 4232;

        public FitScoreBuilderTests()
        {
            credentialRepository.Setup(c => c.IsUserNameUnique(It.IsAny<string>())).Returns(true);
        }

        private FitScoreBuilder CreateBuilder(ProjectHubAssessments projectAssessments) => new FitScoreBuilder(candidateId, projectId, externalClientId, successProfileId, projectAssessments, scoreExtract.Object, textLookup.Object);

        private CandidateExtractResult CreateExtractResult(IDictionary<ScoreDetails, ScoreResult> scores) =>
            new CandidateExtractResult
            {
                Candidates = new List<CandidateResult>
                {
                    new CandidateResult
                    {
                        Projects = new List<ProjectResult>
                        {
                            new ProjectResult
                            {
                                Scores = scores
                            }
                        }
                    }
                }
            };

        private string CreateCodename(string code) => $"{code}-name";

        [Fact]
        public void Constructor_NullProjectAssessments_ArgumentNullExceptionThrown()
        {
            Action act = () => CreateBuilder(null);
            act.Should().Throw<ArgumentNullException>();
        }

        [Fact]
        public void Constructor_NullTextLookup_ArgumentNullExceptionThrown()
        {
            Action act = () => new FitScoreBuilder(candidateId, projectId, 1, 1, new ProjectHubAssessments(), null, null);
            act.Should().Throw<ArgumentNullException>();
        }

        [Fact]
        public void BuildCalculatorScores_NoOverallScoreAvailable_EmptyListReturned()
        {
            scoreExtract.Setup(s => s.GetScoresAsync(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(Task.FromResult(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult> { [new ScoreDetails()] = new ScoreResult() })));
            scoreExtract.Setup(s => s.GetScores(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult> { [new ScoreDetails()] = new ScoreResult() }));

            var builder = CreateBuilder(new ProjectHubAssessments());
            var scores = builder.BuildCalculatorScores();
            scores.Should().BeEmpty();
        }

        [Fact]
        public void BuildCalculatorScores_OverallScoreAvailable_CalculatorScoreReturned()
        {
            const int fit = 3;
            var overallScoreResult = new ScoreResult();
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, ScoreKey.OverallFit.ToLower()), new KfasScoreItem { Fit = fit });

            scoreExtract.Setup(s => s.GetScores(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                }));

            var builder = CreateBuilder(new ProjectHubAssessments());
            var scores = builder.BuildCalculatorScores();
            scores.Count().Should().Be(1);
            scores.First().ScoreType.Should().Be(CalculatorScoreType.Fit);
            scores.First().Score.Should().Be(fit);
            scores.First().Assessments.Should().Be(CalculatorScoreGroup.HubFit);
        }

        [Fact]
        public void BuildAssessmentResults_OneAbilityOnly_AbilityOnlyResult()
        {
            // Combo of overall ability and verbal fit not accurate, but helps check that the builder isn't doing it's own calculations
            const int overallAbilityFit = 1;
            const int verbalFit = 2;

            var overallScoreResult = new ScoreResult();
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, ScoreKey.Ability.ToLower()), new KfasScoreItem { Fit = overallAbilityFit });
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Ability, ScoreKey.Verbal.ToLower()), new KfasScoreItem { Fit = verbalFit });
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Ability, ScoreKey.Numerical.ToLower()), new KfasScoreItem { Fit = -99 });

            scoreExtract.Setup(s => s.GetScores(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                }));
            textLookup.Setup(t => t.GetBlendedAssessmentName(It.Is<BlendedAssessmentType>(b => b == BlendedAssessmentType.Verbal))).Returns("Verbal");
            textLookup.Setup(t => t.Ability).Returns("Ability");

            var projectAssessments = new ProjectHubAssessments
            {
                Verbal = new Ability
                {
                    AssessmentId = (int)AssessmentType.ElementsVerbal,
                    Measure = true
                }
            };
            var builder = CreateBuilder(projectAssessments);
            var scores = builder.BuildAssessmentResults();
            scores.Count().Should().Be(1);
            scores.First().AssessmentOverall.FitScore.Should().Be(overallAbilityFit);
            scores.First().AssessmentDetails.Count.Should().Be(1);
            scores.First().AssessmentDetails.First().AssessmentDetail.FitScore.Should().Be(verbalFit);
            scores.First().AssessmentDetails.First().AssessmentDetail.Description.Should().Be("Verbal");
        }

        [Fact]
        public void BuildAssessmentResults_CompetenciesOnly_CompetencyOnlyResult()
        {
            const int overallCompFit = 3;
            var compScores = new Dictionary<string, int>
            {
                ["mcx"] = 2,
                ["aco"] = 5
            };

            // Include this comp in the reported list but there won't be a score for it, so it should be ignored
            const string compWithoutScore = "noidea";

            var overallScoreResult = new ScoreResult();
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, ScoreKey.Competencies), new KfasScoreItem { Fit = overallCompFit });
            foreach (var compScore in compScores)
            {
                overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Competency, compScore.Key), new KfasScoreItem { Fit = compScore.Value });
            }

            scoreExtract.Setup(s => s.GetScores(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                }));
            textLookup.Setup(t => t.GetBlendedAssessmentName(It.Is<BlendedAssessmentType>(b => b == BlendedAssessmentType.Competencies))).Returns("Comps");
            textLookup.Setup(t => t.GetCompetencyName(externalClientId, successProfileId, It.Is<string>(s => compScores.ContainsKey(s)))).Returns<int, int, string>((c, p, s) => CreateCodename(s));

            var compList = compScores.Keys.ToList();
            compList.Add(compWithoutScore);
            var projectAssessments = new ProjectHubAssessments
            {
                Behavioural = new Behavioural
                {
                    AssessmentId = (int)AssessmentType.Dimensions,
                    Measure = true,
                    Report = compList
                }
            };
            var builder = CreateBuilder(projectAssessments);
            var scores = builder.BuildAssessmentResults();
            scores.Count().Should().Be(1);
            scores.First().AssessmentOverall.FitScore.Should().Be(overallCompFit);
            scores.First().AssessmentOverall.Summary.Should().NotBeEmpty();
            scores.First().AssessmentDetails.Count.Should().Be(compScores.Count, "only competencies with scores should be listed");

            foreach (var compScore in compScores)
            {
                var result = scores.First().AssessmentDetails.FirstOrDefault(a => a.AssessmentDetail.Description == CreateCodename(compScore.Key));
                result.Should().NotBeNull($"should have a result for code {compScore.Key}");
                result.AssessmentDetail.FitScore.Should().Be(compScore.Value, $"score for {result.AssessmentDetail.Description} should be {compScore.Value}");
            }
            scores.First().AssessmentDetails.Any(a => a.AssessmentDetail.Description == CreateCodename(compWithoutScore)).Should().BeFalse($"there shouldn't be a result for comp code {compWithoutScore}");
        }

        [Fact]
        public void BuildAssessmentResults_TraitsOnly_TraitsOnlyResult()
        {
            const int overallTraitsFit = 3;
            var traitsScores = new Dictionary<string, int>
            {
                ["trait1"] = 1,
                ["trait1234"] = 4
            };

            // Include this trait in the reported list but there won't be a score for it, so it should be ignored
            const string traitWithoutScore = "traitunknown";

            var overallScoreResult = new ScoreResult();
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, ScoreKey.Traits), new KfasScoreItem { Fit = overallTraitsFit });
            foreach (var traitScore in traitsScores)
            {
                overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Traits, traitScore.Key), new KfasScoreItem { Fit = traitScore.Value });
            }

            scoreExtract.Setup(s => s.GetScores(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                }));
            textLookup.Setup(t => t.GetBlendedAssessmentName(It.Is<BlendedAssessmentType>(b => b == BlendedAssessmentType.Traits))).Returns("Traits");
            textLookup.Setup(t => t.GetTraitName(It.Is<string>(s => traitsScores.ContainsKey(s)))).Returns<string>(s => CreateCodename(s));

            var traitList = traitsScores.Keys.ToList();
            traitList.Add(traitWithoutScore);
            var projectAssessments = new ProjectHubAssessments
            {
                Traits = new Traits
                {
                    AssessmentId = (int)AssessmentType.Kf4dTraits,
                    Measure = true,
                    Report = traitList
                }
            };
            var builder = CreateBuilder(projectAssessments);
            var scores = builder.BuildAssessmentResults();
            scores.Count().Should().Be(1);
            scores.First().AssessmentOverall.FitScore.Should().Be(overallTraitsFit);
            scores.First().AssessmentOverall.Summary.Should().NotBeEmpty();
            scores.First().AssessmentDetails.Count.Should().Be(traitsScores.Count, "only traits with scores should be listed");

            foreach (var traitScore in traitsScores)
            {
                var result = scores.First().AssessmentDetails.FirstOrDefault(a => a.AssessmentDetail.Description == CreateCodename(traitScore.Key));
                result.Should().NotBeNull($"should have a result for code {traitScore.Key}");
                result.AssessmentDetail.FitScore.Should().Be(traitScore.Value, $"score for {result.AssessmentDetail.Description} should be {traitScore.Value}");
            }
            scores.First().AssessmentDetails.Any(a => a.AssessmentDetail.Description == CreateCodename(traitWithoutScore)).Should().BeFalse($"there shouldn't be a result for code {traitWithoutScore}");
        }

        [Fact]
        public void BuildAssessmentResults_DriversOnly_DriversOnlyResult()
        {
            var overallDriversFit = 4;
            var driversScores = new Dictionary<string, int>
            {
                ["driver1"] = 3,
                ["driver6"] = 2,
                ["driver8"] = 6
            };

            var overallScoreResult = new ScoreResult();
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, ScoreKey.Drivers), new KfasScoreItem { Fit = overallDriversFit });
            foreach (var driverScore in driversScores)
            {
                overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Drivers, driverScore.Key), new KfasScoreItem { Fit = driverScore.Value });
            }

            scoreExtract.Setup(s => s.GetScores(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                }));
            textLookup.Setup(t => t.GetBlendedAssessmentName(It.Is<BlendedAssessmentType>(b => b == BlendedAssessmentType.Drivers))).Returns("Drivers");
            textLookup.Setup(t => t.GetDriverName(It.Is<string>(s => driversScores.ContainsKey(s)))).Returns<string>(s => CreateCodename(s));

            var projectAssessments = new ProjectHubAssessments
            {
                Drivers = new Drivers
                {
                    AssessmentId = (int)AssessmentType.Kf4dDrivers,
                    Measure = true
                }
            };

            var builder = CreateBuilder(projectAssessments);
            var scores = builder.BuildAssessmentResults();
            scores.Count().Should().Be(1);
            scores.First().AssessmentOverall.FitScore.Should().Be(overallDriversFit);
            scores.First().AssessmentOverall.Summary.Should().NotBeEmpty();
            scores.First().AssessmentDetails.Count.Should().Be(driversScores.Count, "only drivers with scores should be listed");

            foreach (var driverScore in driversScores)
            {
                var result = scores.First().AssessmentDetails.FirstOrDefault(a => a.AssessmentDetail.Description == CreateCodename(driverScore.Key));
                result.Should().NotBeNull($"should have a result for code {driverScore.Key}");
                result.AssessmentDetail.FitScore.Should().Be(driverScore.Value, $"score for {result.AssessmentDetail.Description} should be {driverScore.Value}");
            }
        }

        [Fact]
        public void BuildAssessmentResults_CompetenciesTraitsDriversNumericalVerbalLogical_ExpectedResults()
        {
            const int overallCompFit = 5;
            const int overallDriversFit = 2;
            const int overallTraitsFit = 3;
            const int overallAbilityFit = 1;
            var codeScores = new []
            {
                new { group = ScoreGroup.Competency, code = "ccode1", score = 2 },
                new { group = ScoreGroup.Competency, code = "ccode2", score = 4 },
                new { group = ScoreGroup.Traits, code = "tcode1", score = 1 },
                new { group = ScoreGroup.Traits, code = "tcode2", score = 3 },
                new { group = ScoreGroup.Drivers, code = "dcode1", score = 5 },
                new { group = ScoreGroup.Drivers, code = "dcode2", score = 2 },
                new { group = ScoreGroup.Ability, code = ScoreKey.Numerical, score = 1 },
                new { group = ScoreGroup.Ability, code = ScoreKey.Verbal, score = 2 },
                new { group = ScoreGroup.Ability, code = ScoreKey.Logical, score = 3 }
            };

            var overallScoreResult = new ScoreResult();
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, ScoreKey.Ability), new KfasScoreItem { Fit = overallAbilityFit });
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, ScoreKey.Drivers), new KfasScoreItem { Fit = overallDriversFit });
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, ScoreKey.Competencies), new KfasScoreItem { Fit = overallCompFit });
            overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, ScoreKey.Traits), new KfasScoreItem { Fit = overallTraitsFit });
            foreach (var score in codeScores)
            {
                overallScoreResult.ScoreItems.Add(new ScoreItemKey(score.group, score.code), new KfasScoreItem { Fit = score.score });
            }

            scoreExtract.Setup(s => s.GetScores(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                .Returns(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                {
                    [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                }));
            textLookup.Setup(t => t.GetBlendedAssessmentName(It.IsAny<BlendedAssessmentType>())).Returns<BlendedAssessmentType>(b => b.ToDescription());
            textLookup.Setup(t => t.GetTraitName(It.Is<string>(s => codeScores.Where(sc => sc.group == ScoreGroup.Traits).Select(sc => sc.code).Contains(s)))).Returns<string>(s => CreateCodename(s));
            textLookup.Setup(t => t.GetCompetencyName(externalClientId, successProfileId, It.Is<string>(s => codeScores.Where(sc => sc.group == ScoreGroup.Competency).Select(sc => sc.code).Contains(s)))).Returns<int, int, string>((c, p, s) => CreateCodename(s));
            textLookup.Setup(t => t.GetDriverName(It.Is<string>(s => codeScores.Where(sc => sc.group == ScoreGroup.Drivers).Select(sc => sc.code).Contains(s)))).Returns<string>(s => CreateCodename(s));
            textLookup.Setup(t => t.Ability).Returns("Ability");

            var projectAssessments = new ProjectHubAssessments
            {
                Behavioural = new Behavioural
                {
                    AssessmentId = (int)AssessmentType.Kf4dBehaviours,
                    Measure = true,
                    Report = codeScores.Where(s => s.group == ScoreGroup.Competency).Select(s => s.code).ToList()
                },
                Traits = new Traits
                {
                    AssessmentId = (int)AssessmentType.Kf4dTraits,
                    Measure = true,
                    Report = codeScores.Where(s => s.group == ScoreGroup.Traits).Select(s => s.code).ToList()
                },
                Drivers = new Drivers
                {
                    AssessmentId = (int)AssessmentType.Kf4dDrivers,
                    Measure = true
                },
                Numerical = new Ability
                {
                    AssessmentId = (int)AssessmentType.ElementsNumerical,
                    Measure = true
                },
                Verbal = new Ability
                {
                    AssessmentId = (int)AssessmentType.ElementsVerbal,
                    Measure = true
                },
                Logical = new Ability
                {
                    AssessmentId = (int)AssessmentType.ElementsLogical,
                    Measure = true
                }
            };
            var builder = CreateBuilder(projectAssessments);
            var scores = builder.BuildAssessmentResults();

            // Expect 4 results - comps, traits, drivers, ability
            scores.Count().Should().Be(4);

            // Check competencies, traits and drivers results
            var compResult = scores.FirstOrDefault(s => s.AssessmentOverall.Description == BlendedAssessmentType.Competencies.ToDescription());
            compResult.Should().NotBeNull("compResult");
            var traitResult = scores.FirstOrDefault(s => s.AssessmentOverall.Description == BlendedAssessmentType.Traits.ToDescription());
            traitResult.Should().NotBeNull("traitResult");
            var driversResult = scores.FirstOrDefault(s => s.AssessmentOverall.Description == BlendedAssessmentType.Drivers.ToDescription());
            driversResult.Should().NotBeNull("driversResult");

            VerifyCodeScores(ScoreGroup.Competency, compResult);
            VerifyCodeScores(ScoreGroup.Traits, traitResult);
            VerifyCodeScores(ScoreGroup.Drivers, driversResult);

            // Check ability result
            var abilityResult = scores.FirstOrDefault(s => s.AssessmentOverall.Description == "Ability");
            abilityResult.Should().NotBeNull("abilityResult");
            var abilityTypes = new List<BlendedAssessmentType>
            {
                BlendedAssessmentType.Numerical,
                BlendedAssessmentType.Verbal,
                BlendedAssessmentType.Logical
            };

            foreach (var abilityType in abilityTypes)
            {
                var scoreKey = FitScoreBuilder.AbilityBlendedTypeScoreKey[abilityType];
                var descResult = abilityResult.AssessmentDetails.FirstOrDefault(a => a.AssessmentDetail.Description == scoreKey);
                descResult.Should().NotBeNull($"type: {abilityType}, scoreKey: {scoreKey}");
                descResult.AssessmentDetail.FitScore.Should().Be(codeScores.First(c => c.code == scoreKey).score);
            }

            void VerifyCodeScores(ScoreGroup group, OrderPackageAssessmentResult result)
            {
                var groupCodeScores = codeScores.Where(s => s.group == group);
                result.AssessmentDetails.Count.Should().Be(groupCodeScores.Count(), $"there should be one detail result per code for group {group}");
                foreach (var codeScore in groupCodeScores)
                {
                    var codeResult = result.AssessmentDetails.FirstOrDefault(a => a.AssessmentDetail.Description == CreateCodename(codeScore.code));
                    codeResult.Should().NotBeNull($"should have a result for code {codeScore.code} in group {group}");
                    codeResult.AssessmentDetail.FitScore.Should().Be(codeScore.score, $"score for {codeResult.AssessmentDetail.Description} should be {codeScore.score}");
                }
            }
        }
    }
}
