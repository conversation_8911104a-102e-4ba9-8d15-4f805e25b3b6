﻿using FluentAssertions;
using KornFerry.Services.Content;
using KornFerry.Services.Content.Sjt;
using KornFerry.Services.Content.TextLookup.Sjt;
using KornFerry.Services.Extracts.Service;
using KornFerry.Services.Reporting.Blended.Services;
using KornFerry.Services.Reporting.Blended.Services.General;
using KornFerry.Services.Scoring.Shared.Storage;
using KornFerry.Services.Scoring.Shared.Storage.Models;
using KornFerry.Services.Service.ProductsApi;
using Moq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TalentQ.Integrations.KFATS.Common;
using TalentQ.Integrations.KFATS.Common.Results;
using TalentQ.Integrations.KFATS.Hub;
using TalentQ.Integrations.KFATS.Hub.Results;
using TalentQ.Integrations.KFATS.Hub.Results.Configuration;
using TalentQ.Utilities;
using TQLogic;
using TQLogic.Assessments;
using TQLogic.Assessments.Player;
using TQLogic.Authentication;
using TQLogic.Projects;
using TQLogic.Projects.Hub;
using Xunit;
using static KornFerry.Services.Extracts.Service.ScoreResult;

namespace TalentQ.Integrations.Tests.Unit.KFATS.Hub.Results
{
    public class PackageResultBuilderTests
    {
        // Use strict behaviour to check that only expected methods are called
        private readonly Mock<IProductsApiService> productsApiService = new Mock<IProductsApiService>(MockBehavior.Strict);
        private readonly Mock<IHubRepository> hubRepository = new Mock<IHubRepository>(MockBehavior.Strict);
        private readonly Mock<ICredentialRepository> credentialRepository = new Mock<ICredentialRepository>(MockBehavior.Strict);
        private readonly Mock<IScoreExtract> scoreExtract = new Mock<IScoreExtract>(MockBehavior.Strict);
        private readonly Mock<ICommonTextLookup> textLookup = new Mock<ICommonTextLookup>(MockBehavior.Strict);
        private readonly Mock<ISjtTextLookup> sjtLookup = new Mock<ISjtTextLookup>(MockBehavior.Strict);
        private readonly Mock<IReportNameService> reportNameService = new Mock<IReportNameService>(MockBehavior.Strict);
        private readonly Mock<IReportRepository> reportRepository = new Mock<IReportRepository>(MockBehavior.Strict);
        private readonly Mock<IPlayerRepository> playerRepository = new Mock<IPlayerRepository>();

        public PackageResultBuilderTests()
        {
            credentialRepository.Setup(c => c.IsUserNameUnique(It.IsAny<string>())).Returns(true);
            textLookup.Setup(t => t.Language).Returns(Language);
        }

        private TQUser CreateUser(int candidateId)
        {
            var user = TQUser.CreateBlankUser(credentialRepository.Object);
            user.SetChildren(new Candidate(candidateId, 1));
            return user;
        }

        private Language Language => new Language();

        private PackageResultBuilder CreateBuilder(Package package) =>
            new PackageResultBuilder(
                KfatsHandler.Hub, // Only used to pass into url builder
                package,
                ProductsApiRegion.US,
                scoreExtract.Object,
                hubRepository.Object,
                textLookup.Object,
                sjtLookup.Object,
                reportNameService.Object,
                reportRepository.Object,
                productsApiService.Object,
                playerRepository.Object);

        [Fact]
        public void Constructor_NullPackage_ArgumentNullExceptionThrown()
        {
            Action act = () => CreateBuilder(null);
            act.Should().Throw<ArgumentNullException>();
        }

        [Fact]
        public void Constructor_PackageWithoutResultConfig_ArgumentNullExceptionThrown()
        {
            Action act = () => CreateBuilder(new Package(1));
            act.Should().Throw<ArgumentNullException>();
        }

        [Fact]
        public void Constructor_HandlerNotHub_ArgumentExceptionThrown()
        {
            var nonHubHandler = Enum<KfatsHandler>.ToEnumerable().First(h => !h.IsHub());
            Action act = () => new PackageResultBuilder(
                nonHubHandler,
                new Package(1),
                ProductsApiRegion.US,
                scoreExtract.Object,
                hubRepository.Object,
                textLookup.Object,
                sjtLookup.Object,
                reportNameService.Object,
                reportRepository.Object,
                productsApiService.Object,
                playerRepository.Object);
            act.Should().Throw<ArgumentException>();
        }

        private CandidateExtractResult CreateExtractResult(IDictionary<ScoreDetails, ScoreResult> scores) =>
            new CandidateExtractResult
            {
                Candidates = new List<CandidateResult>
                    {
                        new CandidateResult
                        {
                            Projects = new List<ProjectResult>
                            {
                                new ProjectResult
                                {
                                    Scores = scores
                                }
                            }
                        }
                    }
            };

        [Fact]
        public void Build_FitScores_ResultReturned()
        {
            const int candidateId = -123;
            const int projectId = 321;
            const int fit = 3;

            // ------ Setup
            var projectAssessments = SetupFitScoreBuilderDeps();
            var project = new ProjectHub
            {
                ProjectId = projectId,
                SuccessProfileId = 321123,
                ProjectType = ProjectHubType.Graduate,
                AssessmentsJson = JsonConvert.SerializeObject(projectAssessments),
                Project = new Project
                {
                    ClientOwnerId = -9999
                }
            };
            project.Project.ClientOwner.ExternalRef = "123";
            hubRepository.Setup(h => h.GetProjectHub(project.ProjectId.Value, It.IsAny<Project>())).Returns(project);
          
        // Note - no need to test all overall result config permutations because this is covered in separate tests for the PackageOverallResultBuilder
        var resultConfig = new PackageResultBuilderConfiguration
            {
                IncludePdfUrls = false,
                OverallResult = new OverallResultConfiguration
                {
                    FitScoreGroup = CalculatorScoreGroup.HubFit
                }
            };
            var package = new Package(projectId)
            {
                ProjectId = projectId,
                ResultsConfigJSON = JsonConvert.SerializeObject(resultConfig)
            };

            // ------ Act
            var user = CreateUser(candidateId);           
            var builder = CreateBuilder(package);
            var result = builder.Build(user, new Integrations.KFATS.Common.PackageStatus { Status = Integrations.KFATS.Common.IntegrationStatus.Completed }, "orderinthecourt");

            // ------- Assert
            result.Should().NotBeNull("result");

            result.Overall.Should().NotBeNull("overall");
            result.Overall.FitScore.Should().Be(fit, "overall fit");

            result.Urls.Count.Should().Be(1, "one url");
            result.Urls.First().Type.Should().Be(UrlType.Web, "web url");
            result.Urls.First().Url.Should().NotBeNullOrEmpty("web url");

            result.Assessments.Count.Should().Be(1, "one assessment result");
            result.Assessments.First().AssessmentOverall.Description.Should().Be("Comps");
            result.Assessments.First().AssessmentDetails.Should().NotBeNullOrEmpty("assessment detailed results expected");

            ProjectHubAssessments SetupFitScoreBuilderDeps()
            {
                const int overallCompFit = 3;
                var compScores = new Dictionary<string, int>
                {
                    ["mcx"] = 2,
                    ["aco"] = 5
                };

                var overallScoreResult = new ScoreResult();
                overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, ScoreKey.OverallFit.ToLower()), new KfasScoreItem { Fit = fit });
                overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, ScoreKey.Competencies), new KfasScoreItem { Fit = overallCompFit });
                foreach (var compScore in compScores)
                {
                    overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Competency, compScore.Key), new KfasScoreItem { Fit = compScore.Value });
                }

                scoreExtract.Setup(s => s.GetScoresAsync(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                    .Returns(Task.FromResult(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                    {
                        [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                    })));
                scoreExtract.Setup(s => s.GetScores(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                    .Returns(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                    {
                        [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                    }));
                textLookup.Setup(t => t.GetBlendedAssessmentName(It.Is<BlendedAssessmentType>(b => b == BlendedAssessmentType.Competencies))).Returns("Comps");
                textLookup.Setup(t => t.GetCompetencyName(It.IsAny<int>(), It.IsAny<int>(), It.Is<string>(s => compScores.ContainsKey(s)))).Returns<int, int, string>((c, p, s) => $"{s}-name");

                return new ProjectHubAssessments
                {
                    Behavioural = new Behavioural
                    {
                        AssessmentId = (int)AssessmentType.Dimensions,
                        Measure = true,
                        Report = compScores.Keys.ToList()
                    }
                };
            }
        }

        [Fact]
        public void Build_SjtScores_ExecptedResult()
        {
            const int candidateId = -123;
            const int projectId = 321;
            const int overallFit = 2;
            const int playerTestId = 780924;
            const string playerTestName = "readyplayerone";

            // --- Arrange
            SetupSjtScoreBuilderDeps();
            hubRepository.Setup(h => h.GetProjectHub(projectId, It.IsAny<Project>())).Returns(CreateProjectHub());

            // Note - no need to test all overall result config permutations because this is covered in separate tests for the PackageOverallResultBuilder
            var resultConfig = new PackageResultBuilderConfiguration
            {
                IncludePdfUrls = false,
                OverallResult = new OverallResultConfiguration
                {
                    FitScoreGroup = CalculatorScoreGroup.SJT
                }
            };
            var package = new Package(projectId)
            {
                ProjectId = projectId,
                ResultsConfigJSON = JsonConvert.SerializeObject(resultConfig)
            };

            // --- Act
            var user = CreateUser(candidateId);
            var builder = CreateBuilder(package);
            var result = builder.Build(user, new Integrations.KFATS.Common.PackageStatus { Status = Integrations.KFATS.Common.IntegrationStatus.Completed }, "anorderid");

            // --- Assert
            result.Should().NotBeNull("result expected");
            result.Overall.Should().NotBeNull("overall");
            result.Overall.FitScore.Should().Be(overallFit, "overall fit");

            result.Urls.Count.Should().Be(1, "one url");
            result.Urls.First().Type.Should().Be(UrlType.Web, "web url");
            result.Urls.First().Url.Should().NotBeNullOrEmpty("web url");

            result.Assessments.Count.Should().Be(1, "one assessment result");
            result.Assessments.First().AssessmentOverall.Description.Should().Be(playerTestName);
            result.Assessments.First().AssessmentDetails.Should().NotBeNullOrEmpty("assessment detailed results expected");

            ProjectHub CreateProjectHub()
            {
                return new ProjectHub
                {
                    ProjectId = projectId,
                    ProjectType = ProjectHubType.SJT,
                    ScoreDisplay = ScoreDisplayType.Fit,
                    AssessmentsJson = JsonConvert.SerializeObject(new ProjectHubAssessments
                    {
                        SJT = new SJT
                        {
                            AssessmentId = playerTestId,
                            Measure = true
                        }
                    })
                };
            }

            void SetupSjtScoreBuilderDeps()
            {
                const int overallScaleId = 10;
                const int scaleOneId = 1;
                const int scaleTwoId = 2;
                const int overallPercentile = 80;

                var scales = new List<SjtScale>
                {
                    new SjtScale
                    {
                        Id = overallScaleId,
                        Type = PlayerScaleType.Assessment,
                        Display = true
                    },
                    new SjtScale
                    {
                        Id = scaleOneId,
                        Type = PlayerScaleType.Competency,
                        Name = "something",
                        Display = true
                    },
                    new SjtScale
                    {
                        Id = scaleTwoId,
                        Type = PlayerScaleType.Competency,
                        Name = "something else",
                        Display = true
                    }
                };

                var overallScoreResult = new ScoreResult();
                overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Overall, overallScaleId.ToString()), new KfasScoreItem { Fit = overallFit, Percentile = overallPercentile });
                overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Competency, scaleOneId.ToString()), new KfasScoreItem { Fit = 2, Percentile = 45 });
                overallScoreResult.ScoreItems.Add(new ScoreItemKey(ScoreGroup.Competency, scaleTwoId.ToString()), new KfasScoreItem { Fit = 4, Percentile = 35 });

                scoreExtract.Setup(s => s.GetScoresAsync(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                    .Returns(Task.FromResult(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                    {
                        [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                    })));
                scoreExtract.Setup(s => s.GetScores(It.Is<ExtractScoresRequest>(r => r.CandidateIds.Contains(candidateId) && r.ProjectIds.Contains(projectId))))
                    .Returns(CreateExtractResult(new Dictionary<ScoreDetails, ScoreResult>
                    {
                        [new ScoreDetails { Type = ScoreType.OverallFit }] = overallScoreResult
                    }));

                sjtLookup.Setup(s => s.GetScales(It.IsAny<int>())).Returns(scales);
                sjtLookup.Setup(s => s.GetName()).Returns(playerTestName);
            }
        }
    }
}
