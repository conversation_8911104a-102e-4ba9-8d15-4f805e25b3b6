﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{FD976BB9-A696-4F5E-B58E-91DC3895DAAF}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TQIntMVC</RootNamespace>
    <AssemblyName>TQIntMVC</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>false</UseIISExpress>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>4.0</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <MvcProjectUpgradeChecked>true</MvcProjectUpgradeChecked>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <UseGlobalApplicationHostFile />
    <TargetFrameworkProfile />
    <Use64BitIISExpress />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Deploy|AnyCPU' ">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisIgnoreBuiltInRules>false</CodeAnalysisIgnoreBuiltInRules>
    <PublishDatabases>false</PublishDatabases>
    <DeployIisAppPath>testtrial.talentqgroup.com</DeployIisAppPath>
    <Prefer32Bit>false</Prefer32Bit>
    <DebugSymbols>false</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <Optimize>true</Optimize>
    <PublishDatabases>false</PublishDatabases>
    <DeployIisAppPath>trial.talentqgroup.com</DeployIisAppPath>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Prefer32Bit>false</Prefer32Bit>
    <CodeAnalysisRuleSet>..\TQASStylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Deploy|x86' ">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisIgnoreBuiltInRules>false</CodeAnalysisIgnoreBuiltInRules>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Deploy|x64' ">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisIgnoreBuiltInRules>false</CodeAnalysisIgnoreBuiltInRules>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Deploy|x86' ">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisIgnoreBuiltInRules>false</CodeAnalysisIgnoreBuiltInRules>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>..\TQASStylecop.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Deploy|x64' ">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisIgnoreBuiltInRules>false</CodeAnalysisIgnoreBuiltInRules>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Content|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Content|x86'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Content|x64'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Code|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisIgnoreBuiltInRules>false</CodeAnalysisIgnoreBuiltInRules>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Code|x86'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisIgnoreBuiltInRules>false</CodeAnalysisIgnoreBuiltInRules>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Code|x64'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisIgnoreBuiltInRules>false</CodeAnalysisIgnoreBuiltInRules>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'CAS|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'CAS|x86'">
    <OutputPath>bin\</OutputPath>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'CAS|x64'">
    <OutputPath>bin\</OutputPath>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PublishDatabases>false</PublishDatabases>
    <DeployIisAppPath>testtrial.talentqgroup.com</DeployIisAppPath>
    <Prefer32Bit>false</Prefer32Bit>
    <CodeAnalysisRuleSet>..\TQASStylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime">
      <HintPath>..\packages\Antlr.3.4.1.9004\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="AWS.Logger.Core, Version=3.1.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWS.Logger.Core.3.1.0\lib\net45\AWS.Logger.Core.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.CloudWatchLogs, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWSSDK.CloudWatchLogs.3.7.2.54\lib\net45\AWSSDK.CloudWatchLogs.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.Core, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWSSDK.Core.3.7.11.4\lib\net45\AWSSDK.Core.dll</HintPath>
    </Reference>
    <Reference Include="ceTe.DynamicPDF.40, Version=8.0.0.40, Culture=neutral, PublicKeyToken=09b5ce0d5c0a9d8b, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Libraries\ceTe.DynamicPDF.v8\ceTe.DynamicPDF.40.dll</HintPath>
    </Reference>
    <Reference Include="Common.Logging, Version=*******, Culture=neutral, PublicKeyToken=af08829b84f0328e, processorArchitecture=MSIL">
      <HintPath>..\packages\Common.Logging.3.0.0\lib\net40\Common.Logging.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Common.Logging.Core, Version=*******, Culture=neutral, PublicKeyToken=af08829b84f0328e, processorArchitecture=MSIL">
      <HintPath>..\packages\Common.Logging.Core.3.0.0\lib\net40\Common.Logging.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>..\Libraries\ICSharpCode.SharpZipLib\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.4.2.2\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks.Extensions, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks.Extensions.Desktop, Version=1.0.168.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.Extensions.Desktop.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=1*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.7.15\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="NLog.AWS.Logger, Version=3.1.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWS.Logger.NLog.3.1.0\lib\net45\NLog.AWS.Logger.dll</HintPath>
    </Reference>
    <Reference Include="NLog.Extended, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.Extended.4.7.15\lib\net45\NLog.Extended.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Entity" />
    <Reference Include="System.IO.Compression, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="System.Messaging" />
    <Reference Include="System.Net" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.2\lib\net45\System.Web.Helpers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.2\lib\net45\System.Web.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.2\lib\net45\System.Web.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Routing">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.2\lib\net45\System.Web.WebPages.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.2\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.2\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.0\lib\net40\WebActivatorEx.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="WebGrease">
      <HintPath>..\packages\WebGrease.1.5.2\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Wurfl, Version=1.9.0.1, Culture=neutral, PublicKeyToken=816aeec277aa13b9, processorArchitecture=MSIL">
      <HintPath>..\packages\WURFL_Official_API.1.9.0.1\lib\Wurfl.dll</HintPath>
    </Reference>
    <Reference Include="Wurfl.Aspnet.Extensions, Version=1.9.0.0, Culture=neutral, PublicKeyToken=816aeec277aa13b9, processorArchitecture=MSIL">
      <HintPath>..\packages\WURFL_Official_API.1.9.0.1\lib\Wurfl.Aspnet.Extensions.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Code\AssessmentState.cs" />
    <Compile Include="Code\DesignAnnotations.cs" />
    <Compile Include="Controllers\BrandingController.cs" />
    <Compile Include="Controllers\BrandingPreviewController.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Controllers\CompositeController.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Controllers\DebugController.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Controllers\ErrorController.cs" />
    <Compile Include="Controllers\MultiViewController.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Controllers\PlayerController.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Controllers\PlayerHazardConfidenceState.cs" />
    <Compile Include="Helpers\AssessmentHelper.cs">
      <SubType>Code</SubType>
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Helpers\BindingExtensions.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Helpers\CandidateDebugAuthorizeAttribute.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Helpers\DebugHelper.cs" />
    <Compile Include="Helpers\TQHtmlHelper.cs" />
    <Compile Include="Models\Candidate\Assessment\InstructionsTransitionModel.cs" />
    <Compile Include="Models\Candidate\Home\IntegrationDeviceCompatabilityModel.cs" />
    <Compile Include="Models\Candidate\Home\MobileDevicesModel.cs" />
    <Compile Include="Models\Candidate\Instruction\Assessment\AssessmentHeaderInstructionsModel.cs" />
    <Compile Include="Models\Candidate\Instruction\Assessment\ElementsLogicalInstructionsModel.cs" />
    <Compile Include="Models\Candidate\Instruction\Assessment\ElementsNumericalInstructionsModel.cs" />
    <Compile Include="Models\Candidate\Instruction\Assessment\AspectsAbilityInstructionsModel.cs" />
    <Compile Include="Models\Candidate\Instruction\Assessment\AspectsCheckingInstructionsModel.cs" />
    <Compile Include="Models\Candidate\Instruction\Assessment\ElementsVerbalInstructionsModel.cs" />
    <Compile Include="Models\Candidate\Instruction\InstructionsBaseModel.cs" />
    <Compile Include="Models\Candidate\Instruction\InstructionsPanelModel.cs" />
    <Compile Include="Models\Candidate\Instruction\Assessment\PersonalityInstructionsModel.cs" />
    <Compile Include="Models\Candidate\Instruction\Assessment\ResponsiveActionInstructionsModel.cs" />
    <Compile Include="Models\Candidate\Assessment\PracticeFinishModel.cs" />
    <Compile Include="Models\Candidate\Assessment\ResponsiveActionModel.cs" />
    <Compile Include="Models\Candidate\Debug\PlayerDebugModel.cs" />
    <Compile Include="Models\Candidate\Home\AssessmentModel.cs" />
    <Compile Include="Models\Candidate\Home\BestPracticeModel.cs" />
    <Compile Include="Models\Candidate\Home\MultiViewReviewModel.cs" />
    <Compile Include="Models\Candidate\Instruction\Player\PersonalityGroupInstructionsModel.cs" />
    <Compile Include="Models\Candidate\Instruction\Player\PlayerInstructionsModel.cs" />
    <Compile Include="Models\Candidate\Player\ChoiceAnswerModel.cs" />
    <Compile Include="Models\Candidate\Player\HazardResponseModel.cs" />
    <Compile Include="Models\Candidate\Player\ChoiceResponseModel.cs" />
    <Compile Include="Models\Candidate\Player\CommonModel.cs" />
    <Compile Include="Models\Candidate\Player\ContentDictionaryExtensions.cs" />
    <Compile Include="Models\Candidate\Player\HazardAnswerModel.cs" />
    <Compile Include="Models\Candidate\Player\IAnswer.cs" />
    <Compile Include="Models\Candidate\Player\MessageBoxModel.cs" />
    <Compile Include="Models\Candidate\Player\MostLeastAnswerModel.cs" />
    <Compile Include="Models\Candidate\Player\MostLeastResponseModel.cs" />
    <Compile Include="Models\Candidate\Player\PersonalityGroupRankingAnswerModel.cs" />
    <Compile Include="Models\Candidate\Player\PersonalityGroupRatingAnswerModel.cs" />
    <Compile Include="Models\Candidate\Player\PersonalityItemResponseModel.cs" />
    <Compile Include="Models\Candidate\Player\ProgressModel.cs" />
    <Compile Include="Models\Candidate\Player\RankingAnswerModel.cs" />
    <Compile Include="Models\Candidate\Player\RankingResponseModel.cs" />
    <Compile Include="Models\Candidate\Player\RatingAnswerModel.cs" />
    <Compile Include="Models\Candidate\Player\RatingResponseModel.cs" />
    <Compile Include="Models\Candidate\Player\ResponseModelBase.cs" />
    <Compile Include="Models\Candidate\Player\StemModel.cs" />
    <Compile Include="Models\Candidate\Player\StructuredPracticeInstructionsModel.cs" />
    <Compile Include="Models\Candidate\Player\TimerModel.cs" />
    <Compile Include="Models\Candidate\Player\View\FinishModel.cs" />
    <Compile Include="Models\Candidate\Player\View\HazardConfidenceModel.cs" />
    <Compile Include="Models\Candidate\Player\View\InstructionsModel.cs" />
    <Compile Include="Models\Candidate\Player\View\PersonalityGroupModel.cs" />
    <Compile Include="Models\Candidate\Player\View\PersonalityItemModel.cs" />
    <Compile Include="Models\Candidate\Player\View\QuestionModel.cs" />
    <Compile Include="Models\Candidate\Player\View\StageModel.cs" />
    <Compile Include="Models\Candidate\Player\View\TransitionIntoPracticeModel.cs" />
    <Compile Include="Models\Candidate\Player\View\TransitionIntoRealModel.cs" />
    <Compile Include="Models\Candidate\ResponsiveHeaderModel.cs" />
    <Compile Include="Helpers\ReportHelper.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Controllers\LoginController.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Controllers\AssessmentController.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Controllers\CandidateController.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Helpers\HtmlExtensions.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\AssessmentHeaderModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\AssessmentCompleteModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\AspectsCheckingModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\AspectsAbilityModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\ChoiceModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\IQuestionModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\ITimedItemModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\JudgementRatingModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\JudgementChoiceModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\JudgementStemModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\JudgementMostLeastModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\PersonalityModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\IElementsLogicalModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\ElementsLogicalModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\IElementsVerbalModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\ElementsVerbalModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\ElementsNumericalModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\IAspectsAbilityQuestionModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\IAspectsCheckingQuestionModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\IElementsNumericalModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\InstructionsModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\MessageBoxModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\VerificationModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\BrandingPreview\AspectsAbilityPreviewModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\BrandingPreview\AspectsCheckingPreviewModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\BrandingPreview\AssessmentCompletePreviewModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\BrandingPreview\BioDataPreviewModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\BrandingPreview\BrandingPreviewModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\BrandingPreview\CandidatePreviewModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\BrandingPreview\DataProtectionPreviewModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\BrandingPreview\ElementsLogicalPreviewModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\BrandingPreview\ElementsNumericalPreviewModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\BrandingPreview\ElementsVerbalPreviewModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\BrandingPreview\InstructionsPreviewModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\BrandingPreview\PersonalityPreviewModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\CandidateBaseModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\PracticeResponseModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Assessment\PracticeInstructionsModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Composite\CompositeInstructionsModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Composite\CompositeModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Composite\CompositePracticeResponseModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Composite\CompositePracticeInstructionsModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Composite\SegueModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Debug\AspectsAbilityDebugModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Debug\AspectsCheckingDebugModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Debug\DebugModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Debug\ElementsDebugModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Debug\ElementsLogicalDebugModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Debug\ElementsNumericalDebugModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Debug\ElementsVerbalDebugModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Home\AssessmentTableModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Home\BioDataModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Home\CandidateModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Home\AssessmentRowModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Home\DataProtectionModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Home\DetailsModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Home\HonestyContractModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\MultiView\MultiViewModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\MultiView\MultiViewRowModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\MultiView\MultiViewTableModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Candidate\Progress.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\Error\ErrorModel.cs" />
    <Compile Include="Models\Globals.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\IPracticeInstructionsModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\IPracticeResponseModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="appstatus.html" />
    <Content Include="App_Readme\Readme.txt" />
    <Content Include="browserconfig.xml" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\Styles\BrandingTemplates\Assessment_AspectsJudgementMobile.css" />
    <Content Include="Content\Styles\BrandingTemplates\Assessment_AspectsJudgement.css" />
    <Content Include="Content\Styles\BrandingTemplates\Assessment_AspectsChecking.css" />
    <Content Include="Content\Styles\BrandingTemplates\Assessment_AspectsNumerical.css" />
    <Content Include="Content\Styles\BrandingTemplates\Assessment_AspectsStyles.css" />
    <Content Include="Content\Styles\BrandingTemplates\Assessment_AspectsVerbal.css" />
    <Content Include="Content\Styles\BrandingTemplates\Assessment_Composite.css" />
    <Content Include="Content\Styles\BrandingTemplates\Assessment_Dimensions.css" />
    <Content Include="Content\Styles\BrandingTemplates\Assessment_Drives.css" />
    <Content Include="Content\Styles\BrandingTemplates\Assessment_ElementsLogical.css" />
    <Content Include="Content\Styles\BrandingTemplates\Assessment_ElementsNumerical.css" />
    <Content Include="Content\Styles\BrandingTemplates\Assessment_ElementsVerbal.css" />
    <Content Include="Content\Styles\BrandingTemplates\Assessment_PlayerTest.css" />
    <Content Include="Content\Styles\BrandingTemplates\CandidateMobile.css" />
    <Content Include="Content\Styles\BrandingTemplates\Candidate.css" />
    <Content Include="Content\Styles\BrandingBase.css" />
    <Content Include="Content\Styles\Branding\donotdelete.css" />
    <Content Include="Content\Styles\CandidateBundle.css" />
    <Content Include="Content\Styles\CandidateBundle.min.css">
      <DependentUpon>CandidateBundle.css</DependentUpon>
    </Content>
    <Content Include="Content\Styles\FontAwesome\font-awesome-ie7.min.css" />
    <Content Include="Content\Styles\FontAwesome\font-awesome.min.css" />
    <Content Include="Content\Styles\FontAwesome\font\fontawesome-webfont.svg" />
    <Content Include="Content\Styles\Mobile\jquery.mobile-1.4.0-rc.1.css" />
    <Content Include="Content\Styles\Mobile\jquery.mobile.structure-1.4.0-rc.1.min.css" />
    <Content Include="Content\Styles\Mobile\TalentQ.min.css" />
    <Content Include="Content\Styles\Mobile\TQMobile.css" />
    <Content Include="Content\Styles\Mobile\TQMobileJudgement.css" />
    <Content Include="Content\Styles\Responsive\common\popovers.scss" />
    <Content Include="Content\Styles\Responsive\common\grid.scss" />
    <Content Include="App_Data\wurfl.zip" />
    <Content Include="Content\Styles\Responsive\common\commonWithoutProximaNova.scss" />
    <Content Include="Content\Styles\Responsive\common\fontsWithoutProximaNova.scss" />
    <Content Include="Content\Styles\Responsive\common\variablesWithoutProximaNova.scss" />
    <Content Include="App_Readme\WURFL.chm" />
    <Content Include="App_Readme\wurfl-latest.zip" />
    <None Include="Content\Styles\Responsive\extensions\_bootstrap-extra-placements.scss" />
    <Content Include="Content\Styles\Responsive\main-apac.css">
      <DependentUpon>main-apac.scss</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Responsive\main-apac.min.css">
      <DependentUpon>main-apac.css</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Responsive\main.css">
      <DependentUpon>main.scss</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Responsive\main.min.css">
      <DependentUpon>main.css</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Responsive\mainWithoutProximaNova.css">
      <DependentUpon>mainWithoutProximaNova.scss</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Responsive\mainWithoutProximaNova.min.css">
      <DependentUpon>mainWithoutProximaNova.css</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Tests\Aspects\AspectsAbility.css" />
    <Content Include="Content\Styles\Tests\Aspects\Checking\AspectsChecking.css" />
    <Content Include="Content\Styles\Tests\Aspects\Checking\checking9999.css" />
    <Content Include="Content\Styles\Tests\Aspects\Checking\checkingMaster.css">
      <DependentUpon>checkingMaster.scss</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Tests\Aspects\Checking\checkingMaster.min.css">
      <DependentUpon>checkingMaster.css</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical141.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical142.css" />
    <Content Include="Content\Styles\Responsive\rtl.scss" />
    <Content Include="Content\Styles\Responsive\common\panels.scss" />
    <Content Include="Content\Styles\Responsive\main-apac.scss" />
    <Content Include="Content\Styles\Responsive\mainWithoutProximaNova.scss" />
    <None Include="Content\Styles\Tests\Aspects\Numerical\numericalMaster.scss" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numericalMaster.css">
      <DependentUpon>numericalMaster.scss</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numericalMaster.min.css">
      <DependentUpon>numericalMaster.css</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbalMaster.css">
      <DependentUpon>verbalMaster.scss</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbalMaster.min.css">
      <DependentUpon>verbalMaster.css</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Tests\Elements\ElementsLogical.css" />
    <Content Include="Content\Styles\Tests\Elements\ElementsVerbal.css" />
    <Content Include="Content\Styles\Tests\Elements\ElementsNumerical.css" />
    <Content Include="Content\Styles\Tests\MultiView\MultiView.css" />
    <Content Include="Content\Styles\Tests\Aspects\Judgement\TQJudgement.css" />
    <Content Include="Content\Styles\Tests\TQChoice.css" />
    <Content Include="Content\Styles\Tests\TQComposite.css" />
    <Content Include="Content\Styles\Tests\TQPlayer.css" />
    <Content Include="Content\Styles\TQReset.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical122.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical121.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical120.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical119.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical131.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical132.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical133.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical134.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical135.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical136.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical137.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical138.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical139.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical140.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical123.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical124.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical125.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical126.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical127.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical128.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical129.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical130.css" />
    <Content Include="Content\Styles\Tests\Aspects\Numerical\numerical9998.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal102.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal101.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal111.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal112.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal113.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal114.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal115.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal116.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal117.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal118.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal103.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal104.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal105.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal106.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal107.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal108.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal109.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal110.css" />
    <Content Include="Content\Styles\Tests\Aspects\Verbal\verbal9999.css" />
    <None Include="Content\Styles\Tests\Aspects\Verbal\verbalMaster.scss" />
    <Content Include="Content\Styles\Tests\Aspects\Checking\checking1.css" />
    <Content Include="Content\Styles\Tests\Aspects\Checking\checking2.css" />
    <Content Include="Content\Styles\Tests\Aspects\Checking\checking3.css" />
    <Content Include="Content\Styles\Tests\Aspects\Checking\checking4.css" />
    <Content Include="Content\Styles\Tests\Aspects\Checking\checking5.css" />
    <None Include="Content\Styles\Tests\Aspects\Checking\checkingMaster.scss" />
    <Content Include="Content\Styles\TQPersonality.css" />
    <Content Include="Content\Styles\TQCandidate.css" />
    <Content Include="Content\Styles\TQCandidateDebug.css" />
    <Content Include="Content\Styles\TQForms.css" />
    <Content Include="Content\Styles\TQLayout.css" />
    <Content Include="Content\Styles\TQRightToLeft.css" />
    <Content Include="Content\Styles\TQTests.css" />
    <Content Include="Content\Styles\TQType.css" />
    <Content Include="Content\vendor\bootstrap\scss\tests\jasmine.js" />
    <Content Include="Content\vendor\bootstrap\scss\tests\sass-true\register.js" />
    <Content Include="Content\vendor\bootstrap\scss\tests\sass-true\runner.js" />
    <Content Include="favicon.ico" />
    <Content Include="Fileshare\InterfaceBranding\donotremove.txt" />
    <Content Include="Global.asax" />
    <Content Include="robots.txt" />
    <Content Include="NLog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TransformOnBuild>true</TransformOnBuild>
    </Content>
    <Content Include="Content\Styles\FontAwesome\font\fontawesome-webfont.eot" />
    <Content Include="Content\Styles\FontAwesome\font\fontawesome-webfont.ttf" />
    <Content Include="Content\Styles\FontAwesome\font\fontawesome-webfont.woff" />
    <Content Include="Content\Styles\FontAwesome\font\FontAwesome.otf" />
    <Content Include="bundleconfig.json" />
    <None Include="compilerconfig.json" />
    <None Include="compilerconfig.json.defaults">
      <DependentUpon>compilerconfig.json</DependentUpon>
    </None>
    <Content Include="Content\Styles\Responsive\background-information.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_alerts.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_background-variant.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_border-radius.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_buttons.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_center-block.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_clearfix.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_forms.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_gradients.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_grid-framework.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_grid.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_hide-text.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_image.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_labels.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_list-group.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_nav-divider.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_nav-vertical-align.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_opacity.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_pagination.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_panels.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_progress-bar.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_reset-filter.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_reset-text.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_resize.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_responsive-visibility.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_size.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_tab-focus.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_table-row.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_text-emphasis.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_text-overflow.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_vendor-prefixes.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_alerts.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_badges.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_breadcrumbs.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_button-groups.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_buttons.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_carousel.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_close.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_code.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_component-animations.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_dropdowns.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_forms.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_glyphicons.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_grid.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_input-groups.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_jumbotron.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_labels.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_list-group.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_media.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_mixins.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_modals.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_navbar.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_navs.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_normalize.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_pager.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_pagination.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_panels.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_popovers.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_print.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_progress-bars.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_responsive-embed.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_responsive-utilities.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_scaffolding.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_tables.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_theme.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_thumbnails.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_tooltip.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_type.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_utilities.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_variables.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_wells.scss" />
    <Content Include="Content\Styles\Responsive\common\common.scss" />
    <Content Include="Content\Styles\Responsive\common\fonts.scss" />
    <Content Include="Content\Styles\Responsive\common\forms.scss" />
    <Content Include="Content\Styles\Responsive\common\Forms\checkbox-table.scss" />
    <Content Include="Content\Styles\Responsive\common\Forms\dimensions-re-rank.scss" />
    <Content Include="Content\Styles\Responsive\common\Forms\elements-logical.scss" />
    <Content Include="Content\Styles\Responsive\common\Forms\elements-numerical.scss" />
    <Content Include="Content\Styles\Responsive\common\Forms\response-list.scss" />
    <Content Include="Content\Styles\Responsive\common\icons.scss" />
    <Content Include="Content\Styles\Responsive\common\mixins.scss" />
    <Content Include="Content\Styles\Responsive\common\redefine.scss" />
    <Content Include="Content\Styles\Responsive\common\typography.scss" />
    <Content Include="Content\Styles\Responsive\common\variables.scss" />
    <Content Include="Content\Styles\Responsive\dashboard.scss" />
    <Content Include="Content\Styles\Responsive\layout.scss" />
    <Content Include="Content\Styles\Responsive\main.scss" />
    <Content Include="Content\Styles\Responsive\sjt.scss" />
    <Content Include="Content\Styles\Responsive\_bootstrap-compass.scss" />
    <Content Include="Content\Styles\Responsive\_bootstrap-mincer.scss" />
    <Content Include="Content\Styles\Responsive\_bootstrap-sprockets.scss" />
    <Content Include="Content\Styles\Responsive\_bootstrap.scss" />
    <Content Include="NLog.Release.config">
      <DependentUpon>NLog.config</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Scripts\bootstrap-sprockets.js" />
    <Content Include="Introspector\Introspector.pdf" />
    <Content Include="Introspector\Introspector.ashx" />
    <Content Include="COPYING.evaluation" />
    <Content Include="Content\vendor\bootstrap\scss\bootstrap-grid.scss" />
    <Content Include="Content\vendor\bootstrap\scss\bootstrap-reboot.scss" />
    <Content Include="Content\vendor\bootstrap\scss\bootstrap-utilities.scss" />
    <Content Include="Content\vendor\bootstrap\scss\bootstrap.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_floating-labels.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_form-check.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_form-control.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_form-range.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_form-select.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_form-text.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_input-group.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_labels.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_validation.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_clearfix.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_color-bg.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_colored-links.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_focus-ring.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_icon-link.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_position.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_ratio.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_stacks.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_stretched-link.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_text-truncation.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_visually-hidden.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_vr.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_alert.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_backdrop.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_banner.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_border-radius.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_box-shadow.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_breakpoints.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_buttons.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_caret.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_clearfix.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_color-mode.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_color-scheme.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_container.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_deprecate.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_forms.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_gradients.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_grid.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_image.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_list-group.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_lists.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_pagination.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_reset-text.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_resize.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_table-variants.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_text-truncate.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_transition.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_utilities.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_visually-hidden.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\mixins\_auto-import-of-variables-dark.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\mixins\_box-shadow.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\mixins\_color-contrast.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\mixins\_color-modes.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\mixins\_media-query-color-mode-full.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\mixins\_utilities.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\utilities\_api.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\utilities\_api.scss" />
    <Content Include="Content\vendor\bootstrap\scss\vendor\_rfs.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_accordion.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_alert.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_badge.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_breadcrumb.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_button-group.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_buttons.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_card.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_carousel.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_close.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_containers.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_dropdown.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_forms.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_functions.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_grid.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_helpers.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_images.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_list-group.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_maps.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_mixins.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_modal.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_nav.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_navbar.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_offcanvas.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_pagination.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_placeholders.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_popover.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_progress.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_reboot.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_root.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_spinners.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_tables.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_toasts.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_tooltip.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_transitions.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_type.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_utilities.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_variables-dark.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_variables.scss" />
    <None Include="NLog.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Scripts\jquery-1.9.1-vsdoc.js" />
    <Content Include="Scripts\bootstrap.bundle.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap\alert.js" />
    <Content Include="Scripts\bootstrap\button.js" />
    <Content Include="Scripts\bootstrap\carousel.js" />
    <Content Include="Scripts\bootstrap\collapse.js" />
    <Content Include="Scripts\bootstrap\dropdown.js" />
    <Content Include="Scripts\bootstrap\modal.js" />
    <Content Include="Scripts\bootstrap\popover.js" />
    <Content Include="Scripts\bootstrap\scrollspy.js" />
    <Content Include="Scripts\bootstrap\tab.js" />
    <Content Include="Scripts\bootstrap\tooltip.js" />
    <Content Include="Scripts\Responsive\bootstrap-validator.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\affix.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\alert.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\button.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\carousel.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\collapse.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\dropdown.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\modal.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\popover.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\scrollspy.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\tab.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\tooltip.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\transition.js" />
    <Content Include="Scripts\Responsive\bootstrap-validator.min.js">
      <DependentUpon>bootstrap-validator.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\h5f.js" />
    <Content Include="Scripts\jquery-1.9.1.min.js" />
    <Content Include="Scripts\jquery.scrollTo.min.js" />
    <Content Include="Scripts\Responsive\popover-extra-placements.js" />
    <Content Include="Scripts\Responsive\responsive.js" />
    <Content Include="Scripts\MediaElement\background.png" />
    <Content Include="Scripts\MediaElement\bigplay.png" />
    <Content Include="Scripts\MediaElement\bigplay.svg" />
    <Content Include="Scripts\MediaElement\controls-ted.png" />
    <Content Include="Scripts\MediaElement\controls-wmp-bg.png" />
    <Content Include="Scripts\MediaElement\controls-wmp.png" />
    <Content Include="Scripts\MediaElement\controls.png" />
    <Content Include="Scripts\MediaElement\controls.svg" />
    <Content Include="Scripts\MediaElement\flashmediaelement-cdn.swf" />
    <Content Include="Scripts\MediaElement\flashmediaelement.swf" />
    <Content Include="Scripts\MediaElement\loading.gif" />
    <Content Include="Scripts\MediaElement\mediaelement-and-player.min.js" />
    <Content Include="Scripts\MediaElement\mediaelementplayer.min.css" />
    <Content Include="Scripts\MediaElement\mejs-skins.css" />
    <Content Include="Scripts\MediaElement\silverlightmediaelement.xap" />
    <Content Include="Scripts\Mobile\jquery.mobile-1.4.0-rc.1.js" />
    <Content Include="Scripts\Mobile\jquery.mobile-1.4.0-rc.1.min.js" />
    <Content Include="Scripts\Mobile\TQMobile.js" />
    <Content Include="Scripts\Mobile\TQMobileBioData.js" />
    <Content Include="Scripts\Mobile\TQMobileJudgement.js" />
    <Content Include="Scripts\Mobile\TQMobileTests.js" />
    <Content Include="Scripts\Responsive\ResponsiveBundle.js" />
    <Content Include="Scripts\Responsive\ResponsiveBundle.min.js">
      <DependentUpon>ResponsiveBundle.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQAspectsAbility.min.js">
      <DependentUpon>TQAspectsAbility.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQAspectsChecking.min.js">
      <DependentUpon>TQAspectsChecking.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQBioData.js" />
    <Content Include="Scripts\Responsive\TQAspectsAbility.js" />
    <Content Include="Scripts\Responsive\TQAspectsChecking.js" />
    <Content Include="Scripts\Responsive\TQAspectsCheckingDebug.js" />
    <Content Include="Scripts\Responsive\TQBioData.min.js">
      <DependentUpon>TQBioData.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQHonestyContract.js" />
    <Content Include="Scripts\Responsive\TQElementsLogical.min.js">
      <DependentUpon>TQElementsLogical.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQDataProtection.js" />
    <Content Include="Scripts\Responsive\TQDataProtection.min.js">
      <DependentUpon>TQDataProtection.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQElementsNumerical.js" />
    <Content Include="Scripts\Responsive\TQElementsNumerical.min.js">
      <DependentUpon>TQElementsNumerical.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQElementsVerbal.min.js">
      <DependentUpon>TQElementsVerbal.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQElementsNumericalDebug.js" />
    <Content Include="Scripts\Responsive\TQElementsDebug.js" />
    <Content Include="Scripts\Responsive\TQElementsVerbal.js" />
    <Content Include="Scripts\Responsive\TQElementsVerbalDebug.js" />
    <Content Include="Scripts\Responsive\TQElementsLogical.js" />
    <Content Include="Scripts\Responsive\TQElementsLogicalDebug.js" />
    <Content Include="Scripts\Responsive\TQHonestyContract.min.js">
      <DependentUpon>TQHonestyContract.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQMultiView.js" />
    <Content Include="Scripts\Responsive\TQMultiView.min.js">
      <DependentUpon>TQMultiView.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQPersonality.min.js">
      <DependentUpon>TQPersonality.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQPlayerHazardDebug.js" />
    <Content Include="Scripts\Responsive\TQPlayerHazardDebug.min.js">
      <DependentUpon>TQPlayerHazardDebug.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQPlayerHazard.js" />
    <Content Include="Scripts\Responsive\TQPlayerHazard.min.js">
      <DependentUpon>TQPlayerHazard.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQPlayerChoice.min.js">
      <DependentUpon>TQPlayerChoice.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQPlayerCore.js" />
    <Content Include="Scripts\Responsive\TQPlayerCore.min.js">
      <DependentUpon>TQPlayerCore.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQPlayerDebug.js" />
    <Content Include="Scripts\Responsive\TQPlayerChoice.js" />
    <Content Include="Scripts\Responsive\TQPlayerMostLeast.js" />
    <Content Include="Scripts\Responsive\TQPlayerMostLeast.min.js">
      <DependentUpon>TQPlayerMostLeast.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQPlayerPersonalityGroup.js" />
    <Content Include="Scripts\Responsive\TQPlayerPersonalityGroup.min.js">
      <DependentUpon>TQPlayerPersonalityGroup.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQPlayerRanking.js" />
    <Content Include="Scripts\Responsive\TQPlayerRanking.min.js">
      <DependentUpon>TQPlayerRanking.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQPlayerRating.js" />
    <Content Include="Scripts\Responsive\TQPlayerRating.min.js">
      <DependentUpon>TQPlayerRating.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQTests.js" />
    <Content Include="Scripts\Responsive\TQPersonality.js" />
    <Content Include="Scripts\Responsive\TQTestTimer.js" />
    <Content Include="Scripts\TQBrandingEditor.js" />
    <Content Include="Scripts\TQChoice.js" />
    <Content Include="Scripts\TQComposite.js" />
    <Content Include="Scripts\TQCore.js" />
    <Content Include="Scripts\TQDataProtection.js" />
    <Content Include="Scripts\TQPlayerDebug.js" />
    <Content Include="Scripts\TQElementsDebug.js" />
    <Content Include="Scripts\TQElementsLogical.js" />
    <Content Include="Scripts\TQAspectsChecking.js" />
    <Content Include="Scripts\TQElementsLogicalDebug.js" />
    <Content Include="Scripts\TQElementsVerbal.js" />
    <Content Include="Scripts\jquery-1.5.1.js" />
    <Content Include="Scripts\jquery-1.5.1.min.js" />
    <Content Include="Scripts\jquery-ui-1.8.11.js" />
    <Content Include="Scripts\jquery-ui-1.8.11.min.js" />
    <Content Include="Scripts\jquery.unobtrusive-ajax-1.9.1.min.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate-1.9.1.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive-1.9.1.min.js" />
    <Content Include="Scripts\modernizr-1.7.js" />
    <Content Include="Scripts\modernizr-1.7.min.js" />
    <Content Include="Scripts\TQAspectsAbility.js" />
    <Content Include="Scripts\TQAspectsAbilityDebug.js" />
    <Content Include="Scripts\TQAspectsCheckingDebug.js" />
    <Content Include="Scripts\TQHonestyContract.js" />
    <Content Include="Scripts\TQJudgementChoice.js" />
    <Content Include="Scripts\TQJudgementMostLeast.js" />
    <Content Include="Scripts\TQMultiView.js" />
    <Content Include="Scripts\TQJudgementRating.js" />
    <Content Include="Scripts\TQPersonality.js" />
    <Content Include="Scripts\TQBioData.js" />
    <Content Include="Scripts\TQCandidateDebug.js" />
    <Content Include="Scripts\TQElementsNumerical.js" />
    <Content Include="Scripts\TQElementsNumericalDebug.js" />
    <Content Include="Scripts\TQElementsVerbalDebug.js" />
    <Content Include="Scripts\TQPlayerCore.js" />
    <Content Include="Scripts\TQPlayerRanking.js" />
    <Content Include="Scripts\TQPlayerMostLeast.js" />
    <Content Include="Scripts\TQPlayerRating.js" />
    <Content Include="Scripts\TQTests.js" />
    <Content Include="Views\BrandingPreview\Index.cshtml" />
    <Content Include="Views\Debug\Player\Responsive\TransitionIntoPractice.cshtml" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Views\Shared\Mobile\_BrandedContent.cshtml" />
    <Content Include="Views\Composite\Instructions.cshtml" />
    <Content Include="Views\Composite\_CompositePracticeInstructions.cshtml" />
    <Content Include="Views\Composite\_CompositePracticeResponse.cshtml" />
    <Content Include="Views\Assessment\_Progress.cshtml" />
    <Content Include="Views\Assessment\JudgementChoice.cshtml" />
    <Content Include="Views\Composite\Segue.cshtml" />
    <Content Include="Views\Shared\_CompositeLayout.cshtml" />
    <Content Include="Views\Assessment\_JudgementStem.cshtml" />
    <Content Include="Views\Assessment\JudgementRating.cshtml" />
    <Content Include="Views\Assessment\_Choice.cshtml" />
    <Content Include="Views\Player\Question.cshtml" />
    <Content Include="Views\Player\_QuestionGrid.cshtml" />
    <Content Include="Views\Player\_Stem.cshtml" />
    <Content Include="Views\Player\_QuestionTextOnly.cshtml" />
    <Content Include="Views\Player\_ResponseMostLeast.cshtml" />
    <Content Include="Views\Player\_ResponseRating.cshtml" />
    <Content Include="Views\Player\Instructions.cshtml" />
    <Content Include="Views\Player\_Progress.cshtml" />
    <Content Include="Views\Player\Finish.cshtml" />
    <Content Include="Views\Player\_MessageBox.cshtml" />
    <Content Include="Views\Player\_PracticeInstructions.cshtml" />
    <Content Include="Views\Player\_PracticeResponse.cshtml" />
    <Content Include="Views\Player\_ResponseRanking.cshtml" />
    <Content Include="Views\Shared\_PlayerLayout.cshtml" />
    <Content Include="Web.CdnHeaders.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="TempReports\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\KornFerry.Authentication\KornFerry.Authentication.csproj">
      <Project>{9256BF18-094E-49A7-B961-2344BBA526EF}</Project>
      <Name>KornFerry.Authentication</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services.Commercial\KornFerry.Services.Commercial.csproj">
      <Project>{72046f34-6fa2-4bd1-8208-5eebb9ee2fd1}</Project>
      <Name>KornFerry.Services.Commercial</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services.Scoring\KornFerry.Services.Scoring.csproj">
      <Project>{0db5381c-c553-4cbe-8b1f-7e77e3f9c024}</Project>
      <Name>KornFerry.Services.Scoring</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.Assessments\TalentQ.Assessments.csproj">
      <Project>{78825721-EC6A-4C50-A5CF-56C58F4F9E34}</Project>
      <Name>TalentQ.Assessments</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.Core\TalentQ.Core.csproj">
      <Project>{C5242461-DB97-4878-9D40-F37F2737342C}</Project>
      <Name>TalentQ.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.Reporting.Custom\TalentQ.Reporting.Custom.csproj">
      <Project>{9A378696-FEC2-4BCF-9550-BBB3CD9D722C}</Project>
      <Name>TalentQ.Reporting.Custom</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.Reporting.Pdf\TalentQ.Reporting.Pdf.csproj">
      <Project>{81f40639-6570-45b5-b4b8-72ecfe0565c6}</Project>
      <Name>TalentQ.Reporting.Pdf</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.Reporting.Standard\TalentQ.Reporting.Standard.csproj">
      <Project>{5cc60187-70ea-4beb-ade0-a62f2847e236}</Project>
      <Name>TalentQ.Reporting.Standard</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.Utilities\TalentQ.Utilities.csproj">
      <Project>{3FF20D05-08C2-40DB-A419-510BAA064D35}</Project>
      <Name>TalentQ.Utilities</Name>
    </ProjectReference>
    <ProjectReference Include="..\TQCommon\TQCommon.csproj">
      <Project>{E473FCAA-4E5F-4AED-A7FC-3AD6784BA49A}</Project>
      <Name>TQCommon</Name>
    </ProjectReference>
    <ProjectReference Include="..\TQData\TQData.csproj">
      <Project>{9B94BFD8-1030-4E0A-A29D-4526A459A4D1}</Project>
      <Name>TQData</Name>
    </ProjectReference>
    <ProjectReference Include="..\TQIntShared\TQIntShared.csproj">
      <Project>{94710873-327C-441A-B6E5-1F3B91601A2E}</Project>
      <Name>TQIntShared</Name>
    </ProjectReference>
    <ProjectReference Include="..\TQLogic\TQLogic.csproj">
      <Project>{1398EA65-2390-477E-8ACA-0AF976F077FB}</Project>
      <Name>TQLogic</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Shared\_Footer.cshtml" />
    <Content Include="Views\Candidate\Candidate.cshtml" />
    <Content Include="Views\Candidate\_AssessmentTable.cshtml" />
    <Content Include="Views\Candidate\_AssessmentRow.cshtml" />
    <Content Include="Views\Assessment\_PracticeInstructions.cshtml" />
    <Content Include="Views\Assessment\_PracticeResponse.cshtml" />
    <Content Include="Views\Candidate\DataProtection.cshtml" />
    <Content Include="Views\Candidate\BioData.cshtml" />
    <Content Include="Views\Assessment\Instructions.cshtml" />
    <Content Include="Views\Shared\AssessmentComplete.cshtml" />
    <Content Include="Views\Assessment\_AspectsAbilityQuestion.cshtml" />
    <Content Include="Views\Assessment\_AspectsAbilityControls.cshtml" />
    <Content Include="Views\Assessment\AspectsAbility.cshtml" />
    <Content Include="App_Code\TQContent.cshtml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Views\Assessment\AspectsChecking.cshtml" />
    <Content Include="Views\Assessment\_AspectsCheckingQuestion.cshtml" />
    <Content Include="Views\Assessment\_AspectsCheckingControls.cshtml" />
    <Content Include="Views\Assessment\_AspectsCheckingContent.cshtml" />
    <Content Include="App_Code\TQControls.cshtml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Include="Properties\PublishProfiles\Live.pubxml" />
    <None Include="Properties\PublishProfiles\Local.pubxml" />
    <None Include="Properties\PublishProfiles\Staging.pubxml" />
    <Content Include="Views\Assessment\Personality.cshtml" />
    <Content Include="Views\Debug\Index.cshtml" />
    <Content Include="Views\Debug\AspectsAbility.cshtml" />
    <Content Include="Views\Debug\AspectsChecking.cshtml" />
    <None Include="Scripts\jquery-1.5.1-vsdoc.js" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Views\Candidate\Details.cshtml" />
    <Content Include="Views\Assessment\ElementsNumerical.cshtml" />
    <Content Include="Views\Assessment\_ElementsNumericalContent.cshtml" />
    <Content Include="Views\Assessment\_ElementsNumericalControls.cshtml" />
    <Content Include="Views\Assessment\_ElementsNumericalQuestion.cshtml" />
    <Content Include="Views\Assessment\Verification.cshtml" />
    <Content Include="Views\Debug\ElementsNumerical.cshtml" />
    <Content Include="Views\Assessment\ElementsVerbal.cshtml" />
    <Content Include="Views\Assessment\_ElementsVerbalContent.cshtml" />
    <Content Include="Views\Assessment\_ElementsVerbalControls.cshtml" />
    <Content Include="Views\Assessment\_ElementsVerbalQuestion.cshtml" />
    <Content Include="Views\Assessment\ElementsLogical.cshtml" />
    <Content Include="Views\Assessment\_ElementsLogicalContent.cshtml" />
    <Content Include="Views\Assessment\_ElementsLogicalControls.cshtml" />
    <Content Include="Views\Assessment\_ElementsLogicalQuestion.cshtml" />
    <Content Include="Views\Debug\ElementsVerbal.cshtml" />
    <Content Include="Views\Debug\ElementsLogical.cshtml" />
    <Content Include="Views\MultiView\MultiView.cshtml" />
    <Content Include="Views\Candidate\_MultiViewTable.cshtml" />
    <Content Include="Views\Candidate\_MultiViewRow.cshtml" />
    <Content Include="Views\MultiView\_MultiViewIntro.cshtml" />
    <Content Include="Views\MultiView\_MultiViewQuestionTableRow.cshtml" />
    <Content Include="Views\MultiView\_MultiViewQuestionTable.cshtml" />
    <Content Include="Views\MultiView\_MultiViewControls.cshtml" />
    <Content Include="Views\MultiView\_MultiViewQuestion.cshtml" />
    <Content Include="Views\MultiView\_MultiViewFinalComments.cshtml" />
    <Content Include="Views\Shared\_MessageBox.cshtml" />
    <Content Include="Views\Candidate\HonestyContract.cshtml" />
    <Content Include="Views\Assessment\JudgementMostLeast.cshtml" />
    <Content Include="Views\Candidate\Mobile\BioData.cshtml" />
    <Content Include="Views\Candidate\Mobile\Candidate.cshtml" />
    <Content Include="Views\Candidate\Mobile\DataProtection.cshtml" />
    <Content Include="Views\Candidate\Mobile\Details.cshtml" />
    <Content Include="Views\Candidate\Mobile\HonestyContract.cshtml" />
    <Content Include="Views\Candidate\Mobile\_AssessmentTable.cshtml" />
    <Content Include="Views\Shared\Mobile\AssessmentComplete.cshtml" />
    <Content Include="Views\Shared\Mobile\_Layout.cshtml" />
    <Content Include="Views\Shared\Mobile\_MessageBox.cshtml" />
    <Content Include="Views\Assessment\Mobile\Instructions.cshtml" />
    <Content Include="Views\Assessment\Mobile\JudgementMostLeast.cshtml" />
    <Content Include="Views\Assessment\Mobile\_JudgementContent.cshtml" />
    <Content Include="Views\Assessment\Mobile\_JudgementControls.cshtml" />
    <Content Include="Views\Assessment\Mobile\_JudgementQuestion.cshtml" />
    <Content Include="Views\Assessment\Mobile\_PracticeInstructions.cshtml" />
    <Content Include="Views\Assessment\Mobile\_PracticeResponse.cshtml" />
    <Content Include="Views\Player\_QuestionResponseOnly.cshtml" />
    <Content Include="Views\Debug\Player.cshtml" />
    <Content Include="Views\Debug\Player\Instructions.cshtml" />
    <Content Include="Views\Debug\Player\Question.cshtml" />
    <Content Include="Views\Debug\Player\Finish.cshtml" />
    <Content Include="Views\Debug\Player\_Info.cshtml" />
    <Content Include="Views\Assessment\Responsive\_AspectsAbilityQuestion.cshtml" />
    <Content Include="Views\Assessment\Responsive\_AspectsCheckingContent.cshtml" />
    <Content Include="Views\Assessment\Responsive\_AspectsCheckingQuestion.cshtml" />
    <Content Include="Views\Assessment\Responsive\_ElementsLogicalContent.cshtml" />
    <Content Include="Views\Assessment\Responsive\_ElementsLogicalQuestion.cshtml" />
    <Content Include="Views\Assessment\Responsive\_ElementsNumericalContent.cshtml" />
    <Content Include="Views\Assessment\Responsive\_ElementsNumericalQuestion.cshtml" />
    <Content Include="Views\Assessment\Responsive\_ElementsVerbalContent.cshtml" />
    <Content Include="Views\Assessment\Responsive\_ElementsVerbalQuestion.cshtml" />
    <Content Include="Views\Assessment\Responsive\_PracticeResponse.cshtml" />
    <Content Include="Views\Assessment\Responsive\_Progress.cshtml" />
    <Content Include="Views\Assessment\Responsive\AspectsAbility.cshtml" />
    <Content Include="Views\Assessment\Responsive\AspectsChecking.cshtml" />
    <Content Include="Views\Assessment\Responsive\ElementsLogical.cshtml" />
    <Content Include="Views\Assessment\Responsive\ElementsNumerical.cshtml" />
    <Content Include="Views\Assessment\Responsive\ElementsVerbal.cshtml" />
    <Content Include="Views\Assessment\Responsive\Personality.cshtml" />
    <Content Include="Views\Assessment\Responsive\Verification.cshtml" />
    <Content Include="Views\Candidate\Responsive\BioData.cshtml" />
    <Content Include="Views\Candidate\Responsive\Candidate.cshtml" />
    <Content Include="Views\Candidate\Responsive\PrivacyPolicy.cshtml" />
    <Content Include="Views\Candidate\Responsive\Details.cshtml" />
    <Content Include="Views\Candidate\Responsive\HonestyContract.cshtml" />
    <Content Include="Views\Composite\Responsive\_CompositePracticeInstructions.cshtml" />
    <Content Include="Views\Composite\Responsive\_CompositePracticeResponse.cshtml" />
    <Content Include="Views\Composite\Responsive\Instructions.cshtml" />
    <Content Include="Views\Composite\Responsive\Segue.cshtml" />
    <Content Include="Views\MultiView\Responsive\_MultiViewFinalComments.cshtml" />
    <Content Include="Views\MultiView\Responsive\_MultiViewQuestion.cshtml" />
    <Content Include="Views\MultiView\Responsive\MultiView.cshtml" />
    <Content Include="Views\Player\Responsive\_MessageBox.cshtml" />
    <Content Include="Views\Player\Responsive\_PracticeResponse.cshtml" />
    <Content Include="Views\Player\Responsive\_Progress.cshtml" />
    <Content Include="Views\Player\Responsive\_QuestionGrid.cshtml" />
    <Content Include="Views\Player\Responsive\_QuestionResponseOnly.cshtml" />
    <Content Include="Views\Player\Responsive\_QuestionTextOnly.cshtml" />
    <Content Include="Views\Player\Responsive\_ResponseMostLeast.cshtml" />
    <Content Include="Views\Player\Responsive\_ResponseRanking.cshtml" />
    <Content Include="Views\Player\Responsive\_ResponseRating.cshtml" />
    <Content Include="Views\Player\Responsive\_Stem.cshtml" />
    <Content Include="Views\Player\Responsive\Finish.cshtml" />
    <Content Include="Views\Player\Responsive\Instructions.cshtml" />
    <Content Include="Views\Player\Responsive\Question.cshtml" />
    <Content Include="Views\Shared\Responsive\_CompositeLayout.cshtml" />
    <Content Include="Views\Shared\Responsive\_Layout.cshtml" />
    <Content Include="Views\Shared\Responsive\_MessageBox.cshtml" />
    <Content Include="Views\Shared\Responsive\AssessmentComplete.cshtml" />
    <Content Include="Views\Shared\Responsive\_Header.cshtml" />
    <Content Include="Views\Shared\Responsive\_LayoutFixed.cshtml" />
    <Content Include="Views\Shared\Responsive\_LayoutParent.cshtml" />
    <Content Include="Views\Shared\Responsive\_LayoutDashboard.cshtml" />
    <Content Include="Views\Assessment\Responsive\_AspectsAbilityContent.cshtml" />
    <Content Include="Views\Assessment\Responsive\_PageCounters.cshtml" />
    <Content Include="Views\Shared\Responsive\_OutOfTime.cshtml" />
    <Content Include="Views\Debug\Responsive\Index.cshtml" />
    <Content Include="Views\Debug\Responsive\AspectsAbility.cshtml" />
    <Content Include="Views\Assessment\Responsive\_PersonalityQuestions.cshtml" />
    <Content Include="Views\Assessment\Responsive\_PersonalityRanking.cshtml" />
    <Content Include="Views\Shared\Responsive\_Actions.cshtml" />
    <Content Include="Scripts\Responsive\TQAspectsAbility.min.js.map">
      <DependentUpon>TQAspectsAbility.min.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\bootstrap-validator.min.js.map">
      <DependentUpon>bootstrap-validator.min.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQBioData.min.js.map">
      <DependentUpon>TQBioData.min.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQPersonality.min.js.map">
      <DependentUpon>TQPersonality.min.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQAspectsChecking.min.js.map">
      <DependentUpon>TQAspectsChecking.min.js</DependentUpon>
    </Content>
    <Content Include="Views\Debug\Responsive\ElementsNumerical.cshtml" />
    <Content Include="Views\Debug\Responsive\AspectsChecking.cshtml" />
    <Content Include="Views\Debug\Responsive\ElementsVerbal.cshtml" />
    <Content Include="Views\Debug\Responsive\ElementsLogical.cshtml" />
    <Content Include="Scripts\Responsive\TQElementsNumerical.min.js.map">
      <DependentUpon>TQElementsNumerical.min.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQElementsLogical.min.js.map">
      <DependentUpon>TQElementsLogical.min.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQElementsVerbal.min.js.map">
      <DependentUpon>TQElementsVerbal.min.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQMultiView.min.js.map">
      <DependentUpon>TQMultiView.min.js</DependentUpon>
    </Content>
    <Content Include="Views\Debug\Responsive\AspectsChecking.cshtml" />
    <Content Include="Views\Debug\Responsive\ElementsVerbal.cshtml" />
    <Content Include="Scripts\Responsive\TQPlayerMostLeast.min.js.map">
      <DependentUpon>TQPlayerMostLeast.min.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQPlayerRanking.min.js.map">
      <DependentUpon>TQPlayerRanking.min.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQPlayerRating.min.js.map">
      <DependentUpon>TQPlayerRating.min.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQPlayerCore.min.js.map">
      <DependentUpon>TQPlayerCore.min.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Responsive\TQDataProtection.min.js.map">
      <DependentUpon>TQDataProtection.min.js</DependentUpon>
    </Content>
    <Content Include="Views\Player\Responsive\_OutOfTime.cshtml" />
    <Content Include="Views\Debug\Player\Responsive\_Info.cshtml" />
    <Content Include="Views\Debug\Player\Responsive\Finish.cshtml" />
    <Content Include="Views\Debug\Player\Responsive\Instructions.cshtml" />
    <Content Include="Views\Debug\Player\Responsive\Question.cshtml" />
    <Content Include="Views\Candidate\Responsive\BestPractice.cshtml" />
    <Content Include="Views\Candidate\Responsive\_AssessmentIncomplete.cshtml" />
    <Content Include="Views\Candidate\Responsive\_AssessmentComplete.cshtml" />
    <Content Include="Views\Candidate\Responsive\_MultiViewIncomplete.cshtml" />
    <Content Include="Views\Candidate\Responsive\_MultiViewComplete.cshtml" />
    <Content Include="Views\Assessment\Responsive\_InstructionsPanel.cshtml" />
    <Content Include="Views\Assessment\Responsive\PracticeFinish.cshtml" />
    <Content Include="Views\Shared\Responsive\_LayoutNoTitle.cshtml" />
    <Content Include="Views\Player\Responsive\_InstructionsPanel.cshtml" />
    <Content Include="Views\Assessment\Responsive\_PracticeInstructions.cshtml" />
    <Content Include="Views\Player\Responsive\_PracticeInstructions.cshtml" />
    <Content Include="Views\MultiView\Responsive\_MultiViewInstructions.cshtml" />
    <Content Include="Views\Assessment\Responsive\InstructionsTransition.cshtml" />
    <Content Include="Views\Candidate\Responsive\Compatability.cshtml" />
    <Content Include="Views\Branding\Responsive\Styles.cshtml" />
    <Content Include="Views\Branding\Styles.cshtml" />
    <Content Include="Views\Candidate\Responsive\MobileDevices.cshtml" />
    <Content Include="Views\Player\Responsive\TransitionIntoPractice.cshtml" />
    <Content Include="Views\Debug\Responsive\Player.cshtml" />
    <Content Include="Views\Player\Responsive\TransitionIntoReal.cshtml" />
    <Content Include="Views\Debug\Player\Responsive\TransitionIntoReal.cshtml" />
    <Content Include="Views\Player\Responsive\PersonalityGroup.cshtml" />
    <Content Include="Views\Player\Responsive\_PersonalityQuestions.cshtml" />
    <Content Include="Views\Player\Responsive\_StructuredPracticeInstructions.cshtml" />
    <Content Include="Views\Player\Responsive\_PersonalityRanking.cshtml" />
    <Content Include="Views\Player\Responsive\_QuestionColumn.cshtml" />
    <Content Include="Views\Player\Responsive\_ResponseChoice.cshtml" />
    <Content Include="Views\Debug\Player\Responsive\PersonalityGroup.cshtml" />
    <Content Include="Views\Assessment\Blended\_AspectsAbilityContent.cshtml" />
    <Content Include="Views\Assessment\Blended\_AspectsAbilityQuestion.cshtml" />
    <Content Include="Views\Assessment\Blended\_AspectsCheckingContent.cshtml" />
    <Content Include="Views\Assessment\Blended\_AspectsCheckingQuestion.cshtml" />
    <Content Include="Views\Assessment\Blended\_ElementsLogicalContent.cshtml" />
    <Content Include="Views\Assessment\Blended\_ElementsLogicalQuestion.cshtml" />
    <Content Include="Views\Assessment\Blended\_ElementsNumericalContent.cshtml" />
    <Content Include="Views\Assessment\Blended\_ElementsNumericalQuestion.cshtml" />
    <Content Include="Views\Assessment\Blended\_ElementsVerbalContent.cshtml" />
    <Content Include="Views\Assessment\Blended\_ElementsVerbalQuestion.cshtml" />
    <Content Include="Views\Assessment\Blended\_InstructionsPanel.cshtml" />
    <Content Include="Views\Assessment\Blended\_PageCounters.cshtml" />
    <Content Include="Views\Assessment\Blended\_PersonalityQuestions.cshtml" />
    <Content Include="Views\Assessment\Blended\_PersonalityRanking.cshtml" />
    <Content Include="Views\Assessment\Blended\_PracticeInstructions.cshtml" />
    <Content Include="Views\Assessment\Blended\_PracticeResponse.cshtml" />
    <Content Include="Views\Assessment\Blended\_Progress.cshtml" />
    <Content Include="Views\Assessment\Blended\AspectsAbility.cshtml" />
    <Content Include="Views\Assessment\Blended\AspectsChecking.cshtml" />
    <Content Include="Views\Assessment\Blended\ElementsLogical.cshtml" />
    <Content Include="Views\Assessment\Blended\ElementsNumerical.cshtml" />
    <Content Include="Views\Assessment\Blended\ElementsVerbal.cshtml" />
    <Content Include="Views\Assessment\Blended\InstructionsTransition.cshtml" />
    <Content Include="Views\Assessment\Blended\Personality.cshtml" />
    <Content Include="Views\Assessment\Blended\PracticeFinish.cshtml" />
    <Content Include="Views\Assessment\Blended\Verification.cshtml" />
    <Content Include="Views\Branding\Blended\Styles.cshtml" />
    <Content Include="Views\Candidate\Blended\_AssessmentComplete.cshtml" />
    <Content Include="Views\Candidate\Blended\_AssessmentIncomplete.cshtml" />
    <Content Include="Views\Candidate\Blended\_MultiViewComplete.cshtml" />
    <Content Include="Views\Candidate\Blended\_MultiViewIncomplete.cshtml" />
    <Content Include="Views\Candidate\Blended\BestPractice.cshtml" />
    <Content Include="Views\Candidate\Blended\BioData.cshtml" />
    <Content Include="Views\Candidate\Blended\Candidate.cshtml" />
    <Content Include="Views\Candidate\Blended\Compatability.cshtml" />
    <Content Include="Views\Candidate\Blended\Details.cshtml" />
    <Content Include="Views\Candidate\Blended\HonestyContract.cshtml" />
    <Content Include="Views\Candidate\Blended\MobileDevices.cshtml" />
    <Content Include="Views\Candidate\Blended\PrivacyPolicy.cshtml" />
    <Content Include="Views\Composite\Blended\_CompositePracticeInstructions.cshtml" />
    <Content Include="Views\Composite\Blended\_CompositePracticeResponse.cshtml" />
    <Content Include="Views\Composite\Blended\Instructions.cshtml" />
    <Content Include="Views\Composite\Blended\Segue.cshtml" />
    <Content Include="Views\Debug\Player\Blended\_Info.cshtml" />
    <Content Include="Views\Debug\Player\Blended\Finish.cshtml" />
    <Content Include="Views\Debug\Player\Blended\Instructions.cshtml" />
    <Content Include="Views\Debug\Player\Blended\PersonalityGroup.cshtml" />
    <Content Include="Views\Debug\Player\Blended\Question.cshtml" />
    <Content Include="Views\Debug\Player\Blended\TransitionIntoPractice.cshtml" />
    <Content Include="Views\Debug\Player\Blended\TransitionIntoReal.cshtml" />
    <Content Include="Views\Debug\Blended\AspectsAbility.cshtml" />
    <Content Include="Views\Debug\Blended\AspectsChecking.cshtml" />
    <Content Include="Views\Debug\Blended\ElementsLogical.cshtml" />
    <Content Include="Views\Debug\Blended\ElementsNumerical.cshtml" />
    <Content Include="Views\Debug\Blended\ElementsVerbal.cshtml" />
    <Content Include="Views\Debug\Blended\Index.cshtml" />
    <Content Include="Views\Debug\Blended\Player.cshtml" />
    <Content Include="Views\MultiView\Blended\_MultiViewFinalComments.cshtml" />
    <Content Include="Views\MultiView\Blended\_MultiViewInstructions.cshtml" />
    <Content Include="Views\MultiView\Blended\_MultiViewQuestion.cshtml" />
    <Content Include="Views\MultiView\Blended\MultiView.cshtml" />
    <Content Include="Views\Player\Blended\_InstructionsPanel.cshtml" />
    <Content Include="Views\Player\Blended\_MessageBox.cshtml" />
    <Content Include="Views\Player\Blended\_OutOfTime.cshtml" />
    <Content Include="Views\Player\Blended\_PersonalityQuestions.cshtml" />
    <Content Include="Views\Player\Blended\_PersonalityRanking.cshtml" />
    <Content Include="Views\Player\Blended\_PracticeInstructions.cshtml" />
    <Content Include="Views\Player\Blended\_PracticeResponse.cshtml" />
    <Content Include="Views\Player\Blended\_Progress.cshtml" />
    <Content Include="Views\Player\Blended\_QuestionColumn.cshtml" />
    <Content Include="Views\Player\Blended\_QuestionGrid.cshtml" />
    <Content Include="Views\Player\Blended\_QuestionResponseOnly.cshtml" />
    <Content Include="Views\Player\Blended\_QuestionTextOnly.cshtml" />
    <Content Include="Views\Player\Blended\_ResponseChoice.cshtml" />
    <Content Include="Views\Player\Blended\_ResponseMostLeast.cshtml" />
    <Content Include="Views\Player\Blended\_ResponseRanking.cshtml" />
    <Content Include="Views\Player\Blended\_ResponseRating.cshtml" />
    <Content Include="Views\Player\Blended\_Stem.cshtml" />
    <Content Include="Views\Player\Blended\_StructuredPracticeInstructions.cshtml" />
    <Content Include="Views\Player\Blended\Finish.cshtml" />
    <Content Include="Views\Player\Blended\Instructions.cshtml" />
    <Content Include="Views\Player\Blended\PersonalityGroup.cshtml" />
    <Content Include="Views\Player\Blended\Question.cshtml" />
    <Content Include="Views\Player\Blended\TransitionIntoPractice.cshtml" />
    <Content Include="Views\Player\Blended\TransitionIntoReal.cshtml" />
    <Content Include="Views\Shared\Blended\_Actions.cshtml" />
    <Content Include="Views\Shared\Blended\_CompositeLayout.cshtml" />
    <Content Include="Views\Shared\Blended\_Header.cshtml" />
    <Content Include="Views\Shared\Blended\_Layout.cshtml" />
    <Content Include="Views\Shared\Blended\_LayoutDashboard.cshtml" />
    <Content Include="Views\Shared\Blended\_LayoutFixed.cshtml" />
    <Content Include="Views\Shared\Blended\_LayoutNoTitle.cshtml" />
    <Content Include="Views\Shared\Blended\_LayoutParent.cshtml" />
    <Content Include="Views\Shared\Blended\_MessageBox.cshtml" />
    <Content Include="Views\Shared\Blended\_OutOfTime.cshtml" />
    <Content Include="Views\Shared\Blended\AssessmentComplete.cshtml" />
    <Content Include="Views\Player\Responsive\_QuestionHazard.cshtml" />
    <Content Include="Views\Player\Responsive\_ResponseHazard.cshtml" />
    <Content Include="Views\Player\Responsive\HazardConfidence.cshtml" />
    <Content Include="Views\Error\Index.cshtml" />
    <Content Include="Views\Error\Responsive\Index.cshtml" />
    <Content Include="Views\Error\Blended\Index.cshtml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\packages\AWSSDK.CloudWatchLogs.3.7.2.54\analyzers\dotnet\cs\AWSSDK.CloudWatchLogs.CodeAnalysis.dll" />
    <Analyzer Include="..\packages\StyleCop.Analyzers.1.0.0\analyzers\dotnet\cs\Newtonsoft.Json.dll" />
    <Analyzer Include="..\packages\StyleCop.Analyzers.1.0.0\analyzers\dotnet\cs\StyleCop.Analyzers.CodeFixes.dll" />
    <Analyzer Include="..\packages\StyleCop.Analyzers.1.0.0\analyzers\dotnet\cs\StyleCop.Analyzers.dll" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Label="SlowCheetah">
    <SlowCheetahToolsPath>$([System.IO.Path]::GetFullPath( $(MSBuildProjectDirectory)\..\packages\SlowCheetah.2.5.10.3\tools\))</SlowCheetahToolsPath>
    <SlowCheetah_EnableImportFromNuGet Condition=" '$(SC_EnableImportFromNuGet)'=='' ">true</SlowCheetah_EnableImportFromNuGet>
    <SlowCheetah_NuGetImportPath Condition=" '$(SlowCheetah_NuGetImportPath)'=='' ">$([System.IO.Path]::GetFullPath( $(MSBuildProjectDirectory)\Properties\SlowCheetah\SlowCheetah.Transforms.targets ))</SlowCheetah_NuGetImportPath>
    <SlowCheetahTargets Condition=" '$(SlowCheetah_EnableImportFromNuGet)'=='true' and Exists('$(SlowCheetah_NuGetImportPath)') ">$(SlowCheetah_NuGetImportPath)</SlowCheetahTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>52999</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localcandidate.talentqgroup.com</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="$(SlowCheetahTargets)" Condition="Exists('$(SlowCheetahTargets)')" Label="SlowCheetah" />
  <Import Project="..\packages\OctoPack.3.0.44\tools\OctoPack.targets" Condition="Exists('..\packages\OctoPack.3.0.44\tools\OctoPack.targets')" />
  <Target Name="EnsureOctoPackImported" BeforeTargets="BeforeBuild" Condition="'$(OctoPackImported)' == ''">
    <Error Condition="!Exists('..\packages\OctoPack.3.0.44\tools\OctoPack.targets') And ('$(RunOctoPack)' != '' And $(RunOctoPack))" Text="You are trying to build with OctoPack, but the NuGet targets file that OctoPack depends on is not available on this computer. This is probably because the OctoPack package has not been committed to source control, or NuGet Package Restore is not enabled. Please enable NuGet Package Restore to download them. For more information, see http://go.microsoft.com/fwlink/?LinkID=317567." HelpKeyword="BCLBUILD2001" />
    <Error Condition="Exists('..\packages\OctoPack.3.0.44\tools\OctoPack.targets') And ('$(RunOctoPack)' != '' And $(RunOctoPack))" Text="OctoPack cannot be run because NuGet packages were restored prior to the build running, and the targets file was unavailable when the build started. Please build the project again to include these packages in the build. You may also need to make sure that your build server does not delete packages prior to each build. For more information, see http://go.microsoft.com/fwlink/?LinkID=317568." HelpKeyword="BCLBUILD2002" />
  </Target>
  <Import Project="..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets" Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" />
  <Target Name="EnsureBclBuildImported" BeforeTargets="BeforeBuild" Condition="'$(BclBuildImported)' == ''">
    <Error Condition="!Exists('..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" Text="This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=317567." HelpKeyword="BCLBUILD2001" />
    <Error Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" Text="The build restored NuGet packages. Build the project again to include these packages in the build. For more information, see http://go.microsoft.com/fwlink/?LinkID=317568." HelpKeyword="BCLBUILD2002" />
  </Target>
  <Import Project="..\packages\BuildBundlerMinifier.2.1.258\build\BuildBundlerMinifier.targets" Condition="Exists('..\packages\BuildBundlerMinifier.2.1.258\build\BuildBundlerMinifier.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\BuildBundlerMinifier.2.1.258\build\BuildBundlerMinifier.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\BuildBundlerMinifier.2.1.258\build\BuildBundlerMinifier.targets'))" />
    <Error Condition="!Exists('..\packages\BuildWebCompiler.1.11.315\build\BuildWebCompiler.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\BuildWebCompiler.1.11.315\build\BuildWebCompiler.targets'))" />
  </Target>
  <Import Project="..\packages\BuildWebCompiler.1.11.315\build\BuildWebCompiler.targets" Condition="Exists('..\packages\BuildWebCompiler.1.11.315\build\BuildWebCompiler.targets')" />
</Project>