﻿@using TQIntMVC.Models.Candidate.Home;
@model CandidateModel
@{
    Layout = "/Views/Shared/Blended/_LayoutDashboard.cshtml";
}
@section BrowserTitle
{
    @Model.TextBrowserTitle
}
@section HeroText
{
    <p>
        @Model.TextWelcome
        <br />
        @Model.TextCurrentUserDisplayName
    </p>
}
@section Scripts
{
    <script type="text/javascript">
        $(document).ready(function () {
            $.DashboardSetup();
        });
    </script>
}

@section Footer
{
    @if (Model.HasContactInfo)
    {
        <span class="contact-info">
            <span>@Model.TextAssist @Model.ContactName</span>
            <span>@Model.ContactNumber</span>
            <span>
                <a href="mailto:@Model.ContactEmail" title="@Model.ContactEmail">
                    @Model.ContactEmail
                </a>
            </span>
        </span>
    }

}

<section class="panel-dashboard">
    @* Prep items *@
    <div class="panel-heading">
        <h3 class="panel-title">
            @Model.TextPrepItems
            <span class="toggle">
                <a data-toggle="collapse" data-bs-target="#yourPrepItems" href="#collapseOne" class="@(Model.OpenSection != CandidateModel.DashboardSection.PrepItems ? "collapsed" : "")">
                    <span class="icon-tc-arrow-down"></span>
                    <span class="icon-tc-arrow-up"></span>
                </a>
            </span>
        </h3>
    </div>
    <div id="yourPrepItems" class="panel-collapse collapse @(Model.OpenSection == CandidateModel.DashboardSection.PrepItems  ? "in" : "")">
        <div class="panel-body">
            <div class="prep-items-container">
                @foreach (var action in Model.DashboardActions)
                {
                    <div class="prep-item @action.State.ToString().ToLower() @action.Type">
                        @if (action.State == CandidateModel.DashboardActionState.Incomplete || action.Type == CandidateModel.DashboardActionType.BestPractices)
                        {
                            <a class="square" href="@Url.Action(action.ControllerAction)">
                                @DashboardActionBody(action)
                            </a>
                        }
                        else
                        {
                            <div class="square">
                                @DashboardActionBody(action)
                            </div>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</section>

@* Incomplete assessments & MultiView reviews *@
<section class="panel-dashboard">
    <div class="panel-heading">
        <h3 class="panel-title">
            @Model.TextYourAssessments
            <span class="toggle">
                <a data-toggle="collapse" data-bs-target="#yourAssessments" href="#collapseOne" class="@(Model.OpenSection != CandidateModel.DashboardSection.IncompleteAssessments ? "collapsed" : "")">
                    <span class="icon-tc-arrow-down"></span>
                    <span class="icon-tc-arrow-up"></span>
                </a>
            </span>
        </h3>
    </div>
    <div id="yourAssessments" class="panel-collapse collapse @(Model.OpenSection == CandidateModel.DashboardSection.IncompleteAssessments ? "in" : "")">
        <div class="panel-body">
            @if (Model.AssessmentTableModel.AssessmentsResponsive.Any(a => a.HasCustomTime))
            {
                <p class="assessments-timeadded">@Model.TextTimeAdded</p>
            }

            @foreach (var assessment in Model.AssessmentTableModel.AssessmentsResponsive.Where(a => a.Status != TQLogic.Assessments.AssessmentStatusLegacy.Completed))
            {
                @Html.Partial("_AssessmentIncomplete", assessment)
            }

            @foreach (var review in Model.MultiViewTableModel.ReviewsResponsive.Where(r => r.Status != -1))
            {
                @Html.Partial("_MultiViewIncomplete", review)
            }
        </div>
    </div>
</section>

@* Completed assessments & Multiview Reviews *@
<section class="panel-dashboard">
    <div class="panel-heading">
        <h3 class="panel-title">
            @Model.TextCompletedAssessments
            <span class="toggle">
                <a data-toggle="collapse" data-bs-target="#completedAssessments" href="#collapseOne" class="@(Model.OpenSection != CandidateModel.DashboardSection.CompleteAssessments ? "collapsed" : "")">
                    <span class="icon-tc-arrow-down"></span>
                    <span class="icon-tc-arrow-up"></span>
                </a>
            </span>
        </h3>
    </div>
    <div id="completedAssessments" class="panel-collapse collapse @(Model.OpenSection == CandidateModel.DashboardSection.CompleteAssessments ? "in" : "")" ">
        <div class="panel-body">
            @foreach (var assessment in Model.AssessmentTableModel.AssessmentsResponsive.Where(a => a.Status == TQLogic.Assessments.AssessmentStatusLegacy.Completed))
            {
                @Html.Partial("_AssessmentComplete", assessment)
            }

            @foreach (var review in Model.MultiViewTableModel.ReviewsResponsive.Where(r => r.Status == -1))
            {
                @Html.Partial("_MultiViewComplete", review)
            }
        </div>
    </div>
</section>

@helper DashboardActionBody(CandidateModel.DashboardAction action)
{
    <div class="title">
        <h3>
            @action.Title
        </h3>
        @if (action.State == CandidateModel.DashboardActionState.Completed)
        {
            <div class="state-icon"><span class="icon-tc-check"></span></div>
        }
    </div>
    <div class="status">
        @action.StateText
    </div>
if (action.State == CandidateModel.DashboardActionState.Completed && action.Type == CandidateModel.DashboardActionType.BestPractices)
{
        <div class="status2">
            @Model.TextReadAgain
        </div>
}
}