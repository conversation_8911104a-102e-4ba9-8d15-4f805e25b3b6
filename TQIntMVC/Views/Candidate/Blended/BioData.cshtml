﻿@using TQLogic.BioData
@model TQIntMVC.Models.Candidate.Home.BioDataModel
@section BrowserTitle
{
    @Model.TextBrowserTitle
}
@section PageTitle
{
    @Model.TextPageTitle[0]
}
@section Scripts
{
    @TQContent.Script("Responsive/TQBioData.min.js", Url)
    @*h5f script is a polyfill to get html5 validation working on IE9*@
    <!--[if IE 9]><script src="/Scripts/Responsive/h5f.js"></script><![endif]-->
    @TQContent.Script("Responsive/bootstrap-validator.min.js", Url)
    <script type="text/javascript">
        $(document).ready(function () {
            @* Function from TQBioData.js *@
            $.BioDataFormSetup();
        });
    </script>
}

<section class="panel-background">
    <div class="row">
        <div class="col-sm-12">
            <p class="estimate-time">
                @Model.TextEstimateTaskTime @Model.TextEstimateTaskTimeMinutes
            </p>
            <p>
                @Model.TextInstructions1
            </p>
            <p>
                @Model.TextInstructions2
            </p>
            <p>
                @Model.TextMandatory
            </p>
        </div>
        <div class="col-sm-12">
            @using (Html.BeginForm(Model.FormPostAction, "Candidate", null, FormMethod.Post, new { data_toggle = "validator" }))
            {
                foreach (var group in Model.BioDataOptionGroups)
                {
                    <div class="form-group">
                        @RenderBioDataDisplayName(group)
                        @RenderBioDataValidationMessage(group)
                        @RenderBioDataGroupControls(group)
                    </div>
                }
                <div class="action-buttons">
                    <div class="back">
                        @if (Model.ShowBtnCancel)
                        {
                            @Html.ActionLink(Model.TextBtnCancel, "Candidate", "Candidate")
                        }
                    </div>
                    <div class="submit">
                        <button>@Model.TextBtnSubmit</button>
                    </div>
                </div>
                @Html.Partial("_Actions", new TQIntMVC.Models.Candidate.Assessment.ResponsiveActionModel()
           {
               IsBackwardButtonVisible = Model.ShowBtnCancel,
               BackwardText = Model.TextBtnCancel,
               BackwardUrl = Model.BackButtonUrl,
               IsRightToLeftLanguage = Model.IsRightToLeftLanguage,
               ForwardText = Model.TextBtnSubmit
           })
            }
        </div>
    </div>
</section>

@helper RenderBioDataDisplayName(BioDataOptionGroup group)
        {
    <label class="control-label">
        @if (group.Mandatory)
        {
            <span class="required">@group.DisplayName</span>
        }
        else
        {
            @group.DisplayName
        }
    </label>
}

@helper RenderBioDataValidationMessage(BioDataOptionGroup group)
        {
if (group.Mandatory)
{
        <div class="help-block with-errors">
        </div>
}
}

@helper RenderBioDataGroupControls(BioDataOptionGroup group)
        {
switch (group.Type)
{
    case BioDataOptionGroupType.RadioButtonList:
    case BioDataOptionGroupType.RadioButtons:
        {
            if (group.Subgroups.Any())
            {
                    @RenderBioDataRadioButtonWithSubgroupControls(group)
                }
                else
                {
                    @RenderBioDataRadioButtonControls(group)
            }
            break;
        }
    case BioDataOptionGroupType.List:
            @RenderBioDataListControls(group)
        break;
    case BioDataOptionGroupType.ListResponsiveRadioButton:
            @RenderBioDataListResponsiveRadioButtonControls(group)
        break;

    case BioDataOptionGroupType.FreeText:
            @RenderBioDataFreeTextControls(group)
        break;
    case BioDataOptionGroupType.FreeTextArea:
            @RenderBioDataFreeTextAreaControls(group)
        break;

    case BioDataOptionGroupType.CheckBoxList:
            @RenderBioDataCheckBoxListControls(group)
        break;

    case BioDataOptionGroupType.DualList:
            @RenderBioDataDualListControls(group)
        break;

    default:
        break;
}
}

@helper RenderBioDataRadioButtonControls(BioDataOptionGroup group)
        {
    <div class="btn-toolbar" data-toggle="buttons">
        @foreach (BioDataOption option in group.Options)
        {
            var attributes = new Dictionary<string, object> { { "id", option.HtmlDisplayId } };
            if (group.Mandatory)
            {
                Model.AddValidationHtmlAttributes(attributes);
            }
            string extraClass = group.Type == BioDataOptionGroupType.RadioButtonList ? "btnEdge" : "";
            <label class="btn @extraClass" for="@option.HtmlDisplayId">
                @Html.RadioButton(group.CreateHtmlInputName(), option.BioDataOptionId, attributes)
                @option.Name
            </label>
        }
    </div>
}

@helper RenderBioDataRadioButtonWithSubgroupControls(BioDataOptionGroup group)
{
    <div class="radiosubgroup">
        @foreach (var subGroup in group.Subgroups)
        {
            bool showFreeText = subGroup.Options.Any(o => o.DisplayFreeText);
            const int maxLength = 64;
            string panelId = "panel" + group.BioDataOptionGroupId.ToString() + "-" + subGroup.BioDataOptionSubGroupId.ToString();

            <div class="panel-radiosubgroup collapsed">
                <a class="panel-heading collapsed" data-toggle="collapse" data-bs-target="#@panelId">
                    <h3 class="panel-title">
                        @subGroup.DisplayName
                        <span class="selected-value"></span>
                    </h3>
                </a>
                <div id="@panelId" class="panel-collapse collapse">
                    <div class="panel-body">
                        <div class="form-group">
                            <div class="" data-toggle="buttons">

                                @foreach (BioDataOption option in subGroup.Options)
                                {
                                    var attributes = new Dictionary<string, object> { { "data-hastext", option.DisplayFreeText.ToString().ToLower() }, { "id", option.HtmlDisplayId }, { "data-radiosubgroup", true } };
                                    if (group.Mandatory)
                                    {
                                        Model.AddValidationHtmlAttributes(attributes);
                                    }
                                    <label class="btn" for="@option.HtmlDisplayId">
                                        @Html.RadioButton(group.CreateHtmlInputName(), option.BioDataOptionId, attributes)
                                        @option.Name
                                    </label>
                                }
                            </div>
                        </div>
                        @if (showFreeText)
                        {
                            <div class="form-group">
                                <textarea class="form-control" name="@group.CreateHtmlInputName()" rows="3" maxlength="@maxLength" title="@group.DisplayName" disabled></textarea>
                                <div class="text-area-icon"><span class="icon-tc-check"></span></div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
}

@helper RenderBioDataListControls(BioDataOptionGroup group)
{
    string controlId = "list" + group.Options.First().BioDataOptionId.ToString();
    var attributes = new Dictionary<string, object> { { "id", controlId }, { "class", "form-control" }, { "title", group.DisplayName } };
    if (group.Mandatory)
    {
        Model.AddValidationHtmlAttributes(attributes);
    }
    @Html.DropDownList(group.CreateHtmlInputName(), Model.OptionsToSelectListItems(group.Options), attributes)
}

@helper RenderBioDataListResponsiveRadioButtonControls(BioDataOptionGroup group)
{
    string controlId = "list" + group.Options.First().BioDataOptionId.ToString();
    var attributes = new Dictionary<string, object> { { "id", controlId }, { "class", "form-control" }, { "title", group.DisplayName } };
    if (group.Mandatory)
    {
        Model.AddValidationHtmlAttributes(attributes);
    }

    <div class="radio-dropdown">
        @Html.DropDownList(group.CreateHtmlInputName(), Model.OptionsToSelectListItems(group.Options), attributes)
        <div class="btn-group btn-group-justified" data-toggle="buttons">
            @*This bit will be dynamically filled in at runtime*@
        </div>
    </div>
}

@helper RenderBioDataFreeTextControls(BioDataOptionGroup group)
{
    const int maxLength = 64;
    var attributes = new Dictionary<string, object> { { "maxlength", maxLength }, { "class", "form-control" } };
    if (group.Mandatory)
    {
        Model.AddValidationHtmlAttributes(attributes);
    }

    <div class="row">
        <div class="col-xs-12 col-md-6">
            @Html.TextBox(group.CreateHtmlInputName(), null, attributes)
        </div>
    </div>
}

@helper RenderBioDataFreeTextAreaControls(BioDataOptionGroup group)
{
    const int maxLength = 64;
    var attributes = new Dictionary<string, object> { { "maxlength", maxLength }, { "class", "form-control" }, { "rows", 5 }, { "cols", 80 }, { "title", group.DisplayName } };
    if (group.Mandatory)
    {
        Model.AddValidationHtmlAttributes(attributes);
    }

    <div class="row">
        <div class="col-xs-12 col-md-6">
            @Html.TextArea(group.CreateHtmlInputName(), attributes)
        </div>
    </div>
}

@helper RenderBioDataCheckBoxListControls(BioDataOptionGroup group)
{
    <div class="btn-toolbar" data-toggle="buttons">
        @foreach (BioDataOption option in group.Options)
        {
            var attributes = new Dictionary<string, object> { { "id", option.HtmlDisplayId }, { "value", option.BioDataOptionId } };
            if (group.Mandatory)
            {
                Model.AddValidationHtmlAttributes(attributes);
                attributes.Add("data-validationcheckboxes", true); // custom validator
            }
            <label class="btn" for="@option.HtmlDisplayId">
                @Html.CheckBox(group.CreateHtmlInputName(), false, attributes)
                @option.Name
            </label>
        }
    </div>
}

@helper RenderBioDataDualListControls(BioDataOptionGroup group)
{
    foreach (var subGroup in group.Subgroups)
    {
        <div class="form-inline">
            @{
                // To display text next to dropdown list, group name must begin with [p] or [s] to denote a prefix or suffix to the list.
                bool isPrefix = subGroup.DisplayName.Contains("[p]");
                bool isSuffix = subGroup.DisplayName.Contains("[s]");
                string label = subGroup.DisplayName.Replace("[p]", "").Replace("[s]", "");
                string controlId = "list" + subGroup.Options.First().BioDataOptionId.ToString();
                var attributes = new Dictionary<string, object> { { "id", controlId }, { "class", "form-control" } };
                if (group.Mandatory)
                {
                    Model.AddValidationHtmlAttributes(attributes);
                }
            }

            @if (isPrefix)
            {
                @Html.Encode(label)<span>&nbsp;&nbsp;</span>
            }

            @Html.DropDownList(group.CreateHtmlInputName(), Model.OptionsToSelectListItems(subGroup.Options), attributes)

            @if (isSuffix)
            {
                <span>&nbsp;&nbsp;</span>@Html.Encode(label)
            }
        </div>
    }
}
