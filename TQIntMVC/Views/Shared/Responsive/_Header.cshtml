﻿@model TQIntMVC.Models.Candidate.ResponsiveHeaderModel
<header>
    <nav>
        @if (Model.ResponsiveBranding.HasClientLogo)
        {
            <div class="brand-container">
                <div class="brand">
                    @Html.CdnImage(Model.ResponsiveBranding.ClientLogo, null, new { alt = Model.TextAltLogo }, Model.ResponsiveBranding.CdnVersion)
                </div>
            </div>
        }

        @if (Model.ResponsiveBranding.DisplayKornFerryLogo)
        {
            <div class="logo-center sm">
                @Html.CdnImage(Model.ResponsiveBranding.KornFerryLogoSmall, null, new { alt = "Korn Ferry HayGroup"})
            </div>
            <div class="logo-center md">
                @Html.CdnImage(Model.ResponsiveBranding.KornFerryLogoLarge, null, new { alt = "Korn Ferry HayGroup" })
            </div>
        }
        <div class="menu">
            <div class="user-id">
                @if (Model.ShowMenu)
                {
                    <a href="#" role="button" data-bs-toggle="modal" data-target=".side-menu">
                        @if (Model.IsRightToLeftLanguage)
                        {
                            <span class="icon-tc-hamburger"></span>
                        }
                        <span class="hidden-sm">@Model.TextCurrentUserDisplayName</span>
                        <span class="hidden-md">@Model.TextCurrentUserDisplayNameShort</span>
                        @if (!Model.IsRightToLeftLanguage)
                        {
                            <span class="icon-tc-hamburger"></span>
                        }
                    </a>
                }
                else
                {
                    <div class="user-id-nomenu">
                        <span class="hidden-sm">@Model.TextCurrentUserDisplayName</span>
                        <span class="hidden-md">@Model.TextCurrentUserDisplayNameShort</span>
                    </div>
                }
            </div>
        </div>
        @if (Model.ShowMenu)
        {
            <div class="side-menu">
                <div class="content">
                    <div class="inner">
                        @using (Html.BeginForm("Candidate", "Candidate", FormMethod.Post, new { @class = "form-inline" }))
                        {
                            <div class="language">
                                <div class="lang-label">@Model.TextSetLanguage</div>
                                <div class="dropdown">
                                    <a data-target="#" href="#" id="languageLabel" class="dropdown-toggle" data-bs-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">@Model.TextSelectedLanguage<span class="caret"></span></a>
                                    <ul id="sitelanguagedd" class="dropdown-menu" aria-labelledby="languageLabel">
                                        @foreach (var lang in Model.LanguagesList)
                                        {
                                            <li data-langid="@lang.LanguageID"><a href="#">@lang.LanguageName</a></li>
                                        }
                                    </ul>
                                    <input type="hidden" id="DdlLanguage" name="DdlLanguage" />
                                </div>
                            </div>
                        }
                            <ul class="menu-items">
                                @if (Model.CanResetCandidate)
                                {
                                    <li>
                                        @Html.ActionLink("Reset candidate", "ResetAssessments", "Candidate")
                                    </li>
                                }

                                @if (Model.CanAccessDebugMode)
                                {
                                    <li>
                                        @Html.ActionLink("Go to Debug Mode", "Index", "Debug")
                                    </li>
                                }
                                <li>
                                    @TQControls.LogoutForm(Html, Model.TextSignOut)
                                </li>
                            </ul>
                        @if (Model.HasContactInfo)
                        {
                            <p class="contact-info">
                                <span title="@Model.TextAssist">
                                    @Model.TextAssist
                                </span>
                                <span title="@Model.ContactName">
                                    @Model.ContactName
                                </span>
                                <b>
                                    <span title="@Model.ContactNumber">
                                        @Model.ContactNumber
                                    </span>
                                </b>
                                <a href="mailto:@Model.ContactEmail" title="@Model.ContactEmail">
                                    @Model.ContactEmail
                                </a>
                            </p>
                        }
                    </div>
                </div>
            </div>
        }
    </nav>
</header>