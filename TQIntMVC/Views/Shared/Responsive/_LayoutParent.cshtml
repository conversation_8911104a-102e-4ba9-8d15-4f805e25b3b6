﻿@*This parent layout should be used as a layout by either LayoutHero, LayoutFixed or Layout*@
@using System.Web.Optimization
@model TQIntMVC.Models.Candidate.CandidateBaseModel
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="@Model.CurrentContext.SelectedLanguage.Culture" xml:lang="@Model.CurrentContext.SelectedLanguage.Culture" translate="no">
<head @Html.Raw(TQHtmlHelper.HtmlDirectionAttribute(Model.IsRightToLeftLanguage))>
    <title>@RenderSection("BrowserTitle", true)</title>
    @TQContent.StyleMin(Model.ResponsiveCssFile, Url)
    @TQContent.Script("jquery-1.9.1.min.js", Url)
    @TQContent.Script("bootstrap.bundle.min.js", Url)
    @Scripts.RenderFormat("<script type=\"text/javascript\" src=\"{0}\" crossorigin=\"anonymous\"></script>", "~/Scripts/Bundles/Responsive")
    @RenderSection("Styles", false)
    @RenderSection("ScriptsInHead", false)
    <meta http-equiv="Content-type" content="text/html;charset=UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1, maximum-scale=2">
    <!-- Windows 8/IE11 config -->
    <meta name="msapplication-config" content="none" />
    <!-- Disable Skype click-to-call highlighting -->
    <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
    <!-- Disable MS Edge telephone number highlighting -->
    <meta name="format-detection" content="telephone=no" />
    <noscript>
        <meta http-equiv="refresh" content="0;URL=@Url.Action("Login", "Login")" />
    </noscript>
    <link rel="icon" href="@Url.Content("~/favicon.ico")" />
    @Html.BrandedStylesheet(@Model.ResponsiveBranding)

</head>
@RenderSection("Body", true)
</html>

