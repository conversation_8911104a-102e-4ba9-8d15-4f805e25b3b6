@using TQLogic
@model TQIntMVC.Models.Candidate.CandidateBaseModel
@using TQIntShared.Helpers
@using System.Web.Optimization
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="@Model.CurrentContext.SelectedLanguage.Culture" xml:lang="@Model.CurrentContext.SelectedLanguage.Culture" translate="no">
<head>
    <title>@RenderSection("BrowserTitle", true)</title>
    @Styles.Render("~/Content/Bundles/CandidateBundle")
    @RenderSection("Styles", false)
    @if (Model.IsRightToLeftLanguage)
    {
        @TQContent.Style("TQRightToLeft.css", Url)
    }
    @if (Model.Branding.IsBranded)
    {
        @TQContent.Style("BrandingBase.css", Url)
        @TQContent.Content(Model.Branding.BrandedCSSFilePath, Url)
    }
    @if (Model.Branding.DevelopmentMode && Model.CurrentContext.UserLoggedIn.Candidate.CanAccessBrandingPreviewMode)
    {
        <link rel="stylesheet" href="/Content/Styles/BrandingTemplates/Candidate.css" type="text/css" id="brandingTemplateCSS" />
        <style type="text/css" id="brandingTemplateEdits"></style>
    }
    @RenderSection("BrandingStyles", false)
    @TQContent.Script("jquery-1.9.1.min.js", Url)
    @RenderSection("ScriptsInHead", false)
    <meta http-equiv="Content-type" content="text/html;charset=UTF-8" />
    <!-- no cache -->
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="-1" />
    <!-- Windows 8/IE11 config -->
    <meta name="msapplication-config" content="none" />
    <!-- Disable Skype click-to-call highlighting -->
    <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
    <noscript>
        <meta http-equiv="refresh" content="0;URL=@Url.Action("Login", "Login")" />
    </noscript>
</head>
<body @if (Model.IsRightToLeftLanguage) { @: dir="rtl"
                                                                                }>
    @* Main Wrapper *@
    <div id="main">
        @TQControls.Console(Html)
        @* Page *@
        <div id="page">
            @* Page Header *@
            <div id="header">
                @if (Model.Branding.IsBranded)
                {
                    @Html.BrandedContent("Header");
                }
                @* Logo *@
                <div id="logo">
                    @if (Model.Branding.IsBranded)
                    {
                        @Html.BrandedContent("HeaderLogoImage");
                    }
                    else
                    {
                        @Html.CdnImage("/Content/Images/Candidate/kflogo.jpg", "logo", new { alt = Model.TextAltLogo, title = Model.TextAltLogo })
                    }
                </div>
                @if (Model.Branding.IsBranded && Model.Branding.BrandingModel.HasSection("HeaderLogo2Image"))
                {
                    <div id="logo2">
                        @Html.BrandedContent("HeaderLogo2Image")
                    </div>
                }
                @* Page Title *@
                @RenderSection("PageTitle", false)
                @* Candidate Details *@
                    <span class="userDisplayName">
                        <span title="@Model.TextCurrentUserDisplayName">
                            @Model.TextCurrentUserDisplayName
                        </span>
                        @if (Html.ViewContext.RouteData.Values["action"].ToString() == "Candidate")
                        {
                            @TQControls.LogoutForm(Html, Model.TextLogout, "logoutForm", "logoutLink")
                        }
                    </span>
            </div>
            @* Page Banner *@
            @if (Model.Branding.IsBranded)
            {
                if (Model.Branding.BrandingModel.HasSection("HeaderBannerImage"))
                {
                    <div id="banner">
                        @Html.BrandedContent("HeaderBannerImage")
                    </div>
                }
            }
            else
            {
                <div id="banner">
                    @Html.CdnImage("/Content/Images/Candidate/tqbanner.png", "bannerImage", new { alt = Model.TextAltBanner, title = Model.TextAltBanner, id = "bannerImage" })
                </div>
            }
            @* Page Content *@
            <div id="content">
                @RenderBody()
            </div>
        </div>
    </div>
    @* Talent Q Footer *@
    @Html.Partial("_Footer")

    @* Scripts added to bottom of page for performance gains *@
    @if (Model.Branding.DevelopmentMode && Model.CurrentContext.UserLoggedIn.Candidate.CanAccessBrandingPreviewMode)
    {
        @TQContent.Script("TQBrandingEditor.js", Url)
    }
    @RenderSection("Scripts", false)
</body>



</html>
