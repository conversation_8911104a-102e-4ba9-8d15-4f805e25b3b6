﻿/// <reference path="../jquery-1.9.1.min.js" />
/// <reference path="TQTests.js" />

var h1 = $('#hdnAnswer1');
var h2 = $('#hdnAnswer2');

$(document).ready(function () {
    // Add live region if not present
    if ($('#sr-announcer').length === 0) {
        $('body').append('<div id="sr-announcer" class="sr-only" aria-live="assertive" aria-atomic="true"></div>');
    }

    // Track focus entry
    let lastFocused = null;
    let firstFocusHandled = false;

    $(document).on('keydown', function (e) {
        if (e.key === 'Tab') {
            lastFocused = document.activeElement;
            firstFocusHandled = false;
        }
    });


    $('.form-group input[type=checkbox]').on('focus', function () {
        const answerBlock = $('#answerBlock');

        if (!firstFocusHandled && !answerBlock.has(lastFocused).length) {
            firstFocusHandled = true;
            const $first = $('.form-group input[type=checkbox]').first();
            if (!$(this).is($first)) {
                $first.focus();
            }
        }
    });

    $(".form-group label.btn").on("click", function () {
        HideValidation();

        var checkbox = $(this).find("input[type=checkbox]").first();
        var wasChecked = checkbox.prop("checked");

        if ($(".form-group input[type=checkbox]:checked").length >= m_answersReq && !wasChecked) {
            // Uncheck all items if over max answer limit and trying to check a new item
            $(".form-group input[type=checkbox]:checked").prop("checked", false);
            $(".form-group label.active").removeClass("active");
            clearAnswers();
        }

        // Allow proper toggle instead of always forcing checked=true
        checkbox.prop("checked", !wasChecked);
        setTimeout(function () {
            var isChecked = checkbox.prop("checked");
            var title = checkbox.attr("title") || "Option";
            var message = title + " " + (isChecked ? "checked" : "not checked");
            announceToScreenReader(message);
        }, 0);
        setAnswer(checkbox.val());

        //in practice & when submit has been clicked, provide instant feedback when other checkboxes are clicked.
        //m_validFlag is not used because it is reset in HideValidation()
        if (m_isPractice && m_answered) {
            AnswerPractice();
        }
    });

    // Add keyboard support (Enter and Spacebar)
    $(".form-group label.btn").on("keydown", function (e) {
        if (e.keyCode === 13 || e.keyCode === 32) { 
            e.preventDefault();
            $(this).click(); 
        }
    }).attr('tabindex', '0'); 
});


///
///Announce to Screen reader
///
///text = Message to be announced
///
function announceToScreenReader(text) {
    var $region = $('#sr-announcer');
    $region.text('');
    setTimeout(function () {
        $region.text(text);
    }, 50);
}

///
/// Save/clear the answer from hidden fields
///
/// val = answer to save to/clear from hidden fields
///
function setAnswer(val) {
    var h1v = h1.val();
    var h2v = h2.val();
    //check if either is equal to val, clear if so (unselected)
    if (h1v == val) {
        if (h2v != "0") { //if h2 is not empty, move its value to h1 and clear
            h1.val(h2v);
            h2.val('0');
        }
        else {
            h1.val('0');
        }
        return;
    }
    if (h2v == val) {
        h2.val('0');
        return;
    }
    //check if either is empty (0), populate if so
    if (h1v == "0") {
        h1.val(val);
        return;
    }
    if (h2v == "0") {
        h2.val(val);
        return;
    }
}

function clearAnswers() {
    h1.val('0');
    h2.val('0');
}

///
/// Check whether question has been answererd
///
function AnswerQuestion() {
    if (m_submit) {
        submitResponse();
    }
    else {
        SetQuestion();
    }
    return false;
}

///
/// Submit question response during practice
///
function SetQuestion() {
    // Check whether enough answers have been given
    if (!CheckAnswered()) {
        // Show validation message
        ShowValidation();
        // Show practice validation message
        if (m_isPractice) {
            SetButtonText(3);
            return false;
        }
            // Show real validation message
        else {
            // Set flag to determine whether validation is visible and candidate should continue
            if (!m_validFlag) {
                m_validFlag = true;
                SetButtonText(2);
                return false;
            }
        }
    }
    else if (m_isPractice) {
        // Answer practice and provide feedback text
        if (!m_validFlag && !m_answered) {
            m_validFlag = true;
            AnswerPractice();
            SetButtonText(1);
            m_submit = true;
            return false;
        }
    }
    else {
        // If correct answer or confirm submit, stop timer and return
        TQTests.StopTimer();
    }
    submitResponse();
    return false;
}

///
/// Check correct number of answers given.
///
function CheckAnswered() {
    return $(".form-group input[type=checkbox]:checked").length == m_answersReq;    
}

function AnswerPractice()
{
    m_answered = true;
    var numAnswered = $(".form-group input[type=checkbox]:checked").length;
    if (numAnswered == m_answersReq) {
        for (var x = 0; x < m_answersReq; x++) {
            if ($('input[name="hdnPracticeAnswers"]').get(x).value != $('#answerBlock').find('input[type="checkbox"]:checked').get(x).value) {
                TQTests.ShowAnswerResponse(2);
                return;
            }
        }
        TQTests.ShowAnswerResponse(1);
    }
}