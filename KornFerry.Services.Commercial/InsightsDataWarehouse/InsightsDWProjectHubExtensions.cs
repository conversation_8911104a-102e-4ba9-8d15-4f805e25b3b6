﻿using NLog;
using TQLogic.Clients;
using TQLogic.Jobs.Client;
using TQLogic.Projects;

namespace KornFerry.Services.Commercial.InsightsDataWarehouse
{
    /// <summary>
    /// Extensions for the project hub to trigger the Insights Data Warehouse
    /// </summary>
    public static class InsightsDWProjectHubExtensions
    {
        private static Logger log = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// Trigger the project update job for the Insights Data Warehouse
        /// </summary>
        /// <param name="projectHub">The project this is extending</param>
        /// <param name="userId">The user to trigger the job</param>
        public static void SendProjectUpdateToInsightsDW(this ProjectHub projectHub, int userId)
        {
            if (!projectHub.ProjectTypeSupportedByInsightsDataWarehouse())
            {
                log.Debug($"Project type {projectHub.ProjectType} is not supported by Insights Data Warehouse, skipping project update job.");
                return;
            }

            var jobData = new
            {
                ProjectId = projectHub.ProjectId.Value,
            };

            JobClient.Default.SendJob(userId, "Hub/InsightsDataWarehouse/Project", jobData);
        }

        /// <summary>
        /// Determines if the project type is supported by the Insights Data Warehouse.
        /// </summary>
        /// <param name="projectHub">The project this is extending</param>
        /// <returns>True, the data should be sent to the IDW, false the IDW does not support the project type.</returns>
        public static bool ProjectTypeSupportedByInsightsDataWarehouse(this ProjectHub projectHub)
        {
            return projectHub.ProjectType != TQLogic.Projects.Hub.ProjectHubType.SJT;
        }
    }
}
