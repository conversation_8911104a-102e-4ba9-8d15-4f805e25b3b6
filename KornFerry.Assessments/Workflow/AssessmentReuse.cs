﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using TQCommon;
using TQLogic;
using TQLogic.Assessments;
using TQLogic.Jobs.Client;
using TQLogic.Projects;
using TQLogic.Projects.Hub;
using KornFerry.Assessments.Validity;
using KornFerry.Services.Commercial.UnitLogging;
using KornFerry.Services.Commercial.InsightsDataWarehouse;
using TQLogic.Proctoring;

namespace KornFerry.Assessments.Workflow
{
    /// <summary>
    /// Workflow for reusing an assessment.
    /// </summary>
    public class AssessmentReuse
    {
        private static readonly NLog.Logger log = NLog.LogManager.GetCurrentClassLogger();

        private readonly IAssessmentRepository assessmentRepository;
        private readonly IHubRepository hubRepository;
        private readonly IAssessmentValidity assessmentValidity;

        /// <summary>
        /// Instantiate an instance of <see cref="AssessmentReuse"/>
        /// </summary>
        /// <param name="assessmentRepository">Repository for getting assessment details from the database</param>
        /// <param name="hubRepository">Repository for dealing with hub projects</param>
        public AssessmentReuse(IAssessmentRepository assessmentRepository = null, IHubRepository hubRepository = null, IAssessmentValidity assessmentValidity = null)
        {
            this.assessmentRepository = assessmentRepository ?? new AssessmentDbRepository();
            this.hubRepository = hubRepository ?? new HubDbRepository();
            this.assessmentValidity = assessmentValidity ?? new DefaultTwoYearAssessmentValidity();
        }

        /// <summary>
        /// Workflow for reusing an assessment
        /// </summary>
        /// <param name="assessmentId">Id of the assessment to complete</param>
        /// <param name="assessmentToReuseId">Id of the completed assessment to reuse</param>
        /// <param name="loggedOnCandidate">The TQUser of the candidate the reuse is for</param>
        public Task ReuseAssessmentAsync(int assessmentId, int assessmentToReuseId, TQUser loggedOnCandidate)
            => ReuseAssessment(assessmentId, assessmentToReuseId, loggedOnCandidate, true);

        /// <summary>
        /// Workflow for reusing an assessment
        /// </summary>
        /// <param name="assessmentId">Id of the assessment to complete</param>
        /// <param name="assessmentToReuseId">Id of the completed assessment to reuse</param>
        /// <param name="loggedOnCandidate">The TQUser of the candidate the reuse is for</param>
        public void ReuseAssessment(int assessmentId, int assessmentToReuseId, TQUser loggedOnCandidate)
            => ReuseAssessment(assessmentId, assessmentToReuseId, loggedOnCandidate, false).GetAwaiter().GetResult();

        /// <summary>
        /// Workflow for reusing an assessment
        /// </summary>
        /// <param name="assessmentId">Id of the assessment to complete</param>
        /// <param name="assessmentToReuseId">Id of the completed assessment to reuse</param>
        /// <param name="loggedOnCandidate">The TQUser of the candidate the reuse is for</param>
        private async Task ReuseAssessment(int assessmentId, int assessmentToReuseId, TQUser loggedOnCandidate, bool async)
        {
            IEnumerable<ProjectHub> projects;
            AssessmentDetails assessmentDetails;
            AssessmentDetails assessmentToReuseDetails;
            if (async)
            {
                var projectsTask = hubRepository.GetProjectHubsUsingAssessmentAsync(assessmentId, true);
                var assessmentDetailsTask = assessmentRepository.GetAssessmentDetailsAsync(assessmentId);
                var assessmentToReuseDetailsTask = assessmentRepository.GetAssessmentDetailsAsync(assessmentToReuseId);

                projects = await projectsTask;
                assessmentDetails = await assessmentDetailsTask;
                assessmentToReuseDetails = await assessmentToReuseDetailsTask;
            }
            else
            {
                projects = hubRepository.GetProjectHubsUsingAssessment(assessmentId, true);
                assessmentDetails = assessmentRepository.GetAssessmentDetails(assessmentId);
                assessmentToReuseDetails = assessmentRepository.GetAssessmentDetails(assessmentToReuseId);
            }
            

            // Check request is valid (correct candidate, within validity period, etc.)
            Check.IsEqual(assessmentDetails.CandidateId, loggedOnCandidate.CandidateID, $"Assessment passed must be for the logged in candidate (assessment: {assessmentDetails.AssessmentId}, candidate: {assessmentDetails.CandidateId}, expected {loggedOnCandidate.CandidateID})");

            Check.IsEqual(assessmentToReuseDetails.CandidateId, loggedOnCandidate.CandidateID, "Assessment to be reused must be for the logged in candidate");

            Check.IsFalse(projects.Any(p => !p.AllowAssessmentReuse), $"Assessment reuse is not allowed for these projects ({string.Join(", ", projects.Where(p => !p.AllowAssessmentReuse).Select(p => p.ProjectId.ToString()))})");

            Check.IsTrue(assessmentToReuseDetails.Completed.HasValue, $"Assessment {assessmentToReuseDetails.AssessmentId} can't be reused as it hasn't been completed");

            var validity = assessmentValidity.GetValidity(loggedOnCandidate.ClientOwnerID, assessmentToReuseDetails.Type);
            Check.IsGreaterThanOrEqual(assessmentToReuseDetails.Completed.Value, validity, $"Assessment {assessmentToReuseId} was completed at {assessmentToReuseDetails.Completed} but the validity period for reuse begins at {validity}");

            // Replace allocation record (for those projects with reuse assessment enabled)
            var affectedProjectIds = async ? await assessmentRepository.ReuseAssessmentAsync(assessmentId, assessmentToReuseId)
                : assessmentRepository.ReuseAssessment(assessmentId, assessmentToReuseId);
            var affectedProjects = projects.Where(p => affectedProjectIds.Contains(p.ProjectId.Value));

            // Score the assessment via job system, which will then trigger further completion workflow
            ScoreAssessment(loggedOnCandidate, assessmentToReuseDetails, affectedProjects);

            // Log units
            loggedOnCandidate.Candidate.UnitLogAssessmentCompletion(loggedOnCandidate.UserID, assessmentToReuseId, affectedProjectIds);
            loggedOnCandidate.Candidate.InsightsDWAssessmentCompletion(loggedOnCandidate.UserID, assessmentToReuseId, affectedProjectIds);

            // Call this last as it can be a slow update and can timeout occasionally, which stops the above jobs from firing.
            UpdateCandidateProjectsLastActionDate(affectedProjects);

            //Update OverallProctoringScore for proctored projects via job handler
            if(projects.Any(x => x.IsProctoringEnabled))
            {
                JobClient.Default.SendJob(
                            loggedOnCandidate.UserID,
                            "Proctoring/UpdateOverallProctoringScore",
                            new { CandidateUserId = loggedOnCandidate.UserID, AssessmentId = assessmentToReuseId });
            }
        }

        /// <summary>
        /// Returns the Id of the most recent assessment that can be reused to complete <paramref name="test"/>
        /// </summary>
        /// <param name="clientId">Id of the Client</param>
        /// <param name="test">Test details for the test to be started/completed</param>
        /// <param name="allTests">List of all the tests to potentially use for reuse</param>
        /// <returns>AssessmentId for the assessment that can be reused to complete <see cref="test"/></returns>
        public int? GetReuseAssessmentId(int clientId, TestDescriptor test, IEnumerable<TestDescriptor> allTests)
        {
            // Reuse is only possible íf allowed and assessment has not been started
            if (!test.AllowAssessmentReuse || test.Status != AssessmentStatusLegacy.NotStarted)
            {
                return null;
            }

            var validFromDate = assessmentValidity.GetValidity(clientId, test.Type);
            return allTests.Where(x => x.Type == test.Type
                    && x.Status == AssessmentStatusLegacy.Completed
                    && (x.Type != AssessmentType.PlayerTest || x.SubTestId == test.SubTestId)
                    && x.Completed >= validFromDate)
                .OrderByDescending(x => x.Completed)
                .FirstOrDefault()
                ?.AssessmentId;
        }

        private void ScoreAssessment(TQUser loggedOnCandidate, AssessmentDetails assessmentDetails, IEnumerable<ProjectHub> projects)
        {
            Action scoreAction = null;
            var projectIds = projects.Select(p => p.ProjectId.Value);
            if (assessmentDetails.Type.IsScoredExternally())
            {
                scoreAction = () => JobClient.Default.SendJob(
                        loggedOnCandidate.UserID,
                        "Assessments/ScoreReflexAssessment",
                        new { CandidateUserId = loggedOnCandidate.UserID, assessmentDetails.AssessmentId, ProjectIds = projectIds });
            }
            else if (assessmentDetails.Type == AssessmentType.PlayerTest)
            {
                scoreAction = () => JobClient.Default.SendJob(
                            loggedOnCandidate.UserID,
                            "Assessments/ScoreSJT",
                            new { CandidateUserId = loggedOnCandidate.UserID, assessmentDetails.AssessmentId, ProjectIds = projectIds });
            }

            if (scoreAction != null)
            {
                try
                {
                    log.WithContext()
                        .Property("CandidateId", loggedOnCandidate.CandidateID)
                        .Property("AssessmentId", assessmentDetails.AssessmentId)
                        .Debug($"Sending scoring job for candidate {loggedOnCandidate.CandidateID} assessment {assessmentDetails.AssessmentId}");

                    scoreAction.Invoke();
                }
                catch (Exception exception)
                {
                    log.WithContext()
                        .Property("CandidateId", loggedOnCandidate.CandidateID)
                        .Property("AssessmentId", assessmentDetails.AssessmentId)
                        .ErrorException(exception, "An error occurred pushing a scoring job to the Job System");
                }
            }
            else
            {
                foreach (var project in projects)
                {
                    // Trigger the scoring job
                    // AssessmentId is the assessment that's just changed status
                    // NewStatus is the new status of that job
                    // Note - scoring will not always be performed - decision is made inside scoring job
                    HubHelper.InvokeProjectScoring(project, loggedOnCandidate.UserID, assessmentDetails.AssessmentId, AssessmentStatusLegacy.Completed);
                }
            }
        }

        private void UpdateCandidateProjectsLastActionDate(IEnumerable<ProjectHub> projects)
        {
            foreach(var project in projects)
            {
                using (var daProject = new TQData.Project())
                {
                    daProject.SetLastAction(project.ProjectId.Value);
                }
            }
        }
    }
}
