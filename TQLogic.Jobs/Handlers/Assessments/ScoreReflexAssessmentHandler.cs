﻿using KornFerry.Services.Reflex.Models.Responses;
using KornFerry.Services.Reflex.Service.Responses;
using KornFerry.Services.Scoring.Assessments;
using System;
using System.Collections.Generic;
using System.Linq;
using TQCommon;
using TQLogic.Assessments;
using TQLogic.Projects;
using TQLogic.Projects.Hub;

namespace TQLogic.Jobs.Handlers.Assessments
{
    /// <summary>
    /// Scores an assessment completed on the Reflex platform (expect SJT, which is handled by <see cref="ScoreSJTHandler"/>)
    /// Triggers a child <see cref="ScoreProjectHandler"/> job for each affected project.
    /// </summary>
    public class ScoreReflexAssessmentHandler : JobRetryHandlerCompositeBase
    {
        private static readonly NLog.Logger Log = NLog.LogManager.GetCurrentClassLogger();
        private readonly IHubRepository hubRepository = new HubDbRepository();
        private TQUser candidateUser;
        private AssessmentDetails assessment;

        /// <summary>
        /// Gets the ID of the assessment that triggered this job to occur.
        /// </summary>
        public int AssessmentId { get; set; }

        /// <summary>
        /// Gets the assessment we're dealing with.
        /// </summary>
        public AssessmentDetails Assessment => assessment ?? (assessment = CandidateUser.Candidate.GetAssessmentDetailsById(AssessmentId));

        /// <summary>
        /// Gets the candidate we're dealing with.
        /// </summary>
        public TQUser CandidateUser => candidateUser ?? (candidateUser = new TQUser(CandidateUserId));

        /// <summary>
        /// Gets and sets the user ID of the candidate to score the assessment for.
        /// </summary>
        public int CandidateUserId { get; set; }

        /// <summary>
        /// Gets or sets the reflex scope, required to retrieve the responses for.
        /// </summary>
        public string ReflexScope { get; set; }

        /// <summary>
        /// Whether or not this request is for a rescore, i.e. scoring has previously been done
        /// </summary>
        public bool IsRescore { get; set; }

        /// <summary>
        /// Whether or not this job should create child jobs.
        /// If false, only work done within this job will be executed.
        /// </summary>
        public bool CreateChildJobs { get; set; } = true;

        /// <summary>
        /// Score the assessment in the context of a specific project. Optional.
        /// If null all projects containing the assessment will be scored
        /// </summary>
        public int? ProjectId { get; set; }

        /// <summary>
        /// Score the assessment in the context of a list of specific projects. Optional.
        /// If null and <see cref="ProjectId"/> is empty then all projects containing the assessment will be scored.
        /// </summary>
        public IEnumerable<int> ProjectIds { get; set; }

        /// <summary>
        /// Override this method to execute the derived Job Handler.
        /// </summary>
        /// <returns>A status change event for completed to trigger the next steps in the workflow.</returns>
        protected override IEnumerable<ActionResult> ExecuteMany()
        {
            var actions = new List<ActionResult>();
            var scoringResult = ExternalScoreResult.None;
            var projects = new Lazy<IEnumerable<ProjectHub>>(() => GetAffectedProjects());
            BaseBlendedHandler.SetProductsApiRegion(CandidateUser.ClientOwnerID);
            if (Assessment.Type.IsScoredExternally())
            {
                CheckRetrieveResponses();
                scoringResult = ScoreExternally(projects.Value);
            }

            // Only trigger score project if there are 'new' scores for child blended handlers to react to.
            // Or the norm is not supported as this can be the last assessment to score in an additional SP blended rescore job
            // which results in the none of the project scores or reports being generated for this candidate.
            if (CreateChildJobs
                && (scoringResult.HasFlag(ExternalScoreResult.Scored) || scoringResult.HasFlag(ExternalScoreResult.NormNotSupported)))
            {
                foreach (var project in projects.Value)
                {
                    var jobAction = new Action<int, string, object>((cid, h, d) => actions.Add(RequestChildJob(h, d)));
                    HubHelper.InvokeProjectScoring(project, CandidateUserId, AssessmentId, AssessmentStatusLegacy.Completed, jobAction);
                }
            }

            actions.Add(Completed());
            return actions;
        }

        /// <summary>
        /// Gets a list of projects that this assessment is associated to and therefore affects.
        /// If a <see cref="ProjectId"/> is specified then only that project is returned.
        /// </summary>
        /// <returns>List of affected projects</returns>
        private IEnumerable<ProjectHub> GetAffectedProjects()
        {
            var blendedProjectIds = CandidateUser.Candidate.GetBlendedProjectIdsUsingAssessment(AssessmentId, true);
            var blendedProjects = blendedProjectIds.Select(bp => hubRepository.GetProjectHub(bp));
            var expiredProjects = blendedProjects.Where(ph => ph.Project.LockedOutByDeadline);

            if (expiredProjects.Any())
            {
                Log.WithContext(Context).Info($"Expired projects with ids {expiredProjects.Select(p => p.ProjectId.Value).ToCommaString()} in candidate's projects that uses the assessmentId {AssessmentId} will be ignored");
            }

            blendedProjects = blendedProjects.Where(ph => !ph.Project.LockedOutByDeadline);

            if ((ProjectId ?? 0) > 0)
            {
                blendedProjects = blendedProjects.Where(b => b.ProjectId == ProjectId.Value);
                if (!blendedProjects.Any())
                {
                    throw new Exception($"Could not find ProjectId {ProjectId} in candidate's projects that uses the assessmentId {AssessmentId}");
                }
            }
            else if (ProjectIds?.Any() == true)
            {
                blendedProjects = blendedProjects.Where(b => ProjectIds.Contains(b.ProjectId.Value));
                if (blendedProjects.Count() != ProjectIds.Count())
                {
                    throw new Exception($"Could not find Project ids {ProjectIds.Except(blendedProjects.Select(b => b.ProjectId.Value)).ToCommaString()} in candidate's projects that uses the assessmentId {AssessmentId}");
                }
            }

            return blendedProjects;
        }

        /// <summary>
        /// Scores the externally scored assessments e.g. Modular Kf4D, Experiences and Preferences
        /// </summary>
        /// <param name="projects">List of projects to score the assessment against</param>
        /// <returns>If scoring was done. False if scores already existed</returns>
        private ExternalScoreResult ScoreExternally(IEnumerable<ProjectHub> projects)
        {
            var scored = ExternalScoreResult.None;
            foreach (var project in projects)
            {
                // Stop scoring if SP is not published
                if (!project.IsSuccessProfilePublished) {
                    Log.WithContext(Context).Info($"Project {project.ProjectId} related SP-{project.SuccessProfileId} is not published yet, skipping scoring and reporting.");
                    continue;
                }

                Log.Debug($"Scoring project {project.ProjectId}");
                var hubCandidate = hubRepository.GetProjectHubCandidate(project.ProjectId.Value, CandidateUser.CandidateID);
                var projectScored = new ExternalScoreService(project, hubCandidate).Score(Assessment, hubCandidate.TargetLevel, IsRescore);
                scored |= projectScored;
                              
            }

            return scored;
        }

        /// <summary>
        /// Checks if responses exist and if not retrieves them from the Reflex platform
        /// </summary>
        private void CheckRetrieveResponses()
        {
            if (string.IsNullOrEmpty(ReflexScope))
            {
                Log.WithContext(Context).Info("Reflex scope not provided, ignoring check/retrieval of responses");
                return;
            }

            var responseService = new ResponseService<ReflexResponses>();
            if (responseService.GetResponses(CandidateUser.CandidateID, AssessmentId) != null)
            {
                Log.WithContext(Context).Info("Responses already saved, no need to retrieve");
                return;
            }
            else
            {
                Log.WithContext(Context).Info("Retrieving and saving responses");
                var service = new RetrieveResponses();
                var responses = service.RetrieveJsonResponse(ReflexScope);
                new DbReflexResponseRepository().SaveResponses(AssessmentId, responses);
            }
        }
    }
}