﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TQLogic.Projects.Hub
{
     /// <summary>
     /// Project meta data for KF Searcher.
     /// </summary>
    public class ProjectMetaData
    {
        /// <summary>
        /// Const keys for common metadata items.
        /// </summary>
        public const string CompanyName = "CompanyName";
        public const string SearcherReference = "SearcherReference";
        public const string EngagementId = "EngagementId";
        public const string SiteId = "SiteId";

        /// <summary>
        /// Project meta data <key, value>.
        /// </summary>
        public Dictionary<string, string> Metadata { get; set; }

        public string Get(string name)
        {
            if (Metadata == null) return null;

            // Exact match (case-sensitive)
            if (Metadata.TryGetValue(name, out var value))
            {
                return value;
            }

            // Case-insensitive search
            foreach (var kvp in Metadata)
            {
                if (string.Equals(kvp.Key, name, StringComparison.OrdinalIgnoreCase))
                {
                    return kvp.Value;
                }
            }

            return null;
        }
    }
}
