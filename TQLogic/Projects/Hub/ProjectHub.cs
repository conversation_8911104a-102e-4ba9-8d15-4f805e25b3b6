using Newtonsoft.Json;


using System;
using System.Collections.Generic;
using System.Linq;
using TalentQ.Core.Users.BioData;
using TQLogic.Assessments;
using TQLogic.Projects.Hub;

namespace TQLogic.Projects
{
    /// <summary>
    /// Hub details for a project
    /// </summary>
    public class ProjectHub
    {
        /// <summary>
        /// The project Id
        /// </summary>
        public int? ProjectId { get; set; }

        /// <summary>
        /// The parent project id, used to link a child project with a different success profile id / settings to the parent project.
        /// </summary>
        public int? ParentProjectId { get; set; }

        /// <summary>
        /// Checks if success profile is published
        /// </summary>
        public bool IsSuccessProfilePublished { get; set; }

        /// <summary>
        /// Checks if Project is billable or Non Billable
        /// </summary>
        public bool IsNonBillable { get; set; }

        /// <summary>
        /// Determine if this project is a child success profile project.
        /// </summary>
        public bool IsChildProject => ParentProjectId.HasValue;

        /// <summary>
        /// The type of Products Hub project
        /// </summary>
        public ProjectHubType ProjectType { get; set; }

        /// <summary>
        /// The id of CustomProjectType when <see cref="CustomProjectType"/> = custom
        /// </summary>
        public int? CustomProjectTypeId { get; set; }

        /// <summary>
        /// The name of CustomProjectType when <see cref="CustomProjectType"/> = custom
        /// </summary>
        public string CustomProjectTypeName { get; set; }

        /// <summary>
        /// The Default ScoringNorm of CustomProjectType when <see cref="ReportScoringNormType"/> = custom
        /// </summary>
        public ReportScoringNormType CptDefaultScoringNorm { get; set; }

        /// <summary>
        /// The product type
        /// </summary>
        public HubProductType ProductType { get; set; }

        /// <summary>
        /// External client id, to group projects per client, all external clients and projects are stored under a single KFAS client and this is required to filter the projects externally.
        /// </summary>
        public int? ExternalClientId => HubClientReference.ExternalClientId(Project?.ClientOwner.ExternalRef);

        /// <summary>
        /// An external clients sub group of projects, resources, defined as a sub account.
        /// </summary>
        public string UserGroup => HubClientReference.UserGroup(Project?.ClientOwner.ExternalRef);

        /// <summary>
        /// The name of the platform for the project
        /// </summary>
        public string Platform { get; set; }

        /// <summary>
        /// External user info of the creator of this project (doesn't relate to our user)
        /// </summary>
        public string CreatedByJson { get; set; }

        /// <summary>
        /// External user info of the owner of this project (doesn't relate to our user)
        /// </summary>
        public string OwnerJson { get; set; }

        /// <summary>
        /// External user info of the last modifier of this project (doesn't relate to our user)
        /// </summary>
        public string LastModifiedByJson { get; set; }

        /// <summary>
        /// The email address to sent candidate completion emails to and also the reply to email for invites and reminder emails.
        /// </summary>
        public string NotificationEmail { get; set; }

        /// <summary>
        /// DateTime this project was last modified
        /// </summary>
        public DateTime? LastModified { get; set; }

        /// <summary>
        /// External Id of the Success Profile. Usage depends on <see cref="ProjectType"/>
        /// </summary>
        public int? SuccessProfileId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether success profile has been customized on the Products Hub.
        /// Usage depends on <see cref="SuccessProfileId"/> being used.
        /// </summary>
        public bool? SuccessProfileCustomized { get; set; }

        /// <summary>
        /// Whether or not to include fit score details (we still calculate, this drives the UI)
        /// </summary>
        public bool IncludeFitScore { get; set; }

        /// <summary>
        /// The current level of participants within the project. Usage depends on <see cref="ProjectType"/>
        /// </summary>
        public string CurrentLevel { get; set; }

        /// <summary>
        /// The target level of participants within the project. Usage depends on <see cref="ProjectType"/>
        /// </summary>
        public string TargetLevel { get; set; }

        /// <summary>
        /// Determines if a project contains potential levels and should include potential reports / scoring etc.
        /// No longer based only on Project Type as Assess Projects can contain a mixture of configuration.
        /// </summary>
        public bool HasPotentialLevels => ProjectType == ProjectHubType.Potential ||
                                        (ProductType == HubProductType.TM && !string.IsNullOrEmpty(CurrentLevel) && !string.IsNullOrEmpty(TargetLevel));

        /// <summary>
        /// Whether or not to include derailers in certain reports. Usage depends on <see cref="ProjectType"/>
        /// </summary>
        public bool? IncludeDerailers { get; set; }

        /// <summary>
        /// External location info
        /// </summary>
        public string LocationJson { get; set; }

        /// <summary>
        /// The target scores
        /// </summary>
        public ProjectHubTargets Targets { get; set; }

        /// <summary>
        /// Raw assessments Json to specify details of what assessments to use for a project
        /// </summary>
        public string AssessmentsJson { get; set; }

        /// <summary>
        /// Raw BIC overrides JSON. Optional.
        /// </summary>
        public string BicOverridesJson { get; set; }

        /// <summary>
        /// Id of the reminder notification template.
        /// </summary>
        public int? ReminderNotificationTemplateId { get; set; }

        /// <summary>
        /// Id of the invitation SSO notification template.
        /// Only required when SsoSetting.OptionalDomainEnforced is used
        /// </summary>
        public int? InvitationSSOTemplateId { get; set; }

        /// <summary>
        /// Id of the sso reminder notification template.
        /// </summary>
        public int? ReminderSSONotificationTemplateId { get; set; }

        /// <summary>
        /// the project hub metrics
        /// </summary>
        public ProjectMetrics Metrics { get; set; }

        /// <summary>
        /// Deserialized <see cref="AssessmentsJson"/>
        /// </summary>
        public ProjectHubAssessments Assessments => string.IsNullOrEmpty(AssessmentsJson) ? null : JsonConvert.DeserializeObject<ProjectHubAssessments>(AssessmentsJson);

        /// <summary>
        /// The Job location.
        /// </summary>
        public Location Location => string.IsNullOrEmpty(LocationJson) ? null : JsonConvert.DeserializeObject<Location>(LocationJson);

        /// <summary>
        /// The BIC overrides if used
        /// </summary>
        public ProjectHubBicOverrides BicOverrides => string.IsNullOrEmpty(BicOverridesJson) ? null : JsonConvert.DeserializeObject<ProjectHubBicOverrides>(BicOverridesJson);

        /// <summary>
        /// The score display
        /// </summary>
        public ScoreDisplayType ScoreDisplay { get; set; }

        /// <summary>
        /// Raw norm json to specify the norm settings for a project
        /// </summary>
        public string NormJson { get; set; }

        /// <summary>
        /// Deserialized <see cref="NormJson"/>
        /// </summary>
        public ProjectHubNorm Norm => string.IsNullOrEmpty(NormJson) ? null : JsonConvert.DeserializeObject<ProjectHubNorm>(NormJson);

        /// <summary>
        /// Whether or not this project is for a global role
        /// </summary>
        public bool? IsGlobalRole { get; set; }

        /// <summary>
        /// Raw json of the Best-In-Class scoring result for the project, based on project assessment config
        /// </summary>
        public string BicScoresJson { get; set; }

        /// <summary>
        /// Whether or not this project is read-only from the perspective of the Hub UI.
        /// For example this project is used as an integration package and should not be edited.
        /// </summary>
        public bool IsReadOnly { get; set; }

        /// <summary>
        /// Allow the asssessments to be reused
        /// </summary>
        public bool AllowAssessmentReuse { get; set; }

        /// <summary>
        /// General settings related to the project.
        /// </summary>
        public ProjectSettings Settings { get; set; } = new ProjectSettings();

        /// <summary>
        /// A list of additional Success Profile Ids associated with this project, if any.
        /// </summary>
        public IEnumerable<int> AdditionalSuccessProfileIds { get; set; } = Enumerable.Empty<int>();

        /// <summary>
        /// Indicates whether the project includes a learning agility report or not.
        /// </summary>
        public bool IncludeLearningAgility { get; set; }

        /// <summary>
        /// Participant reports in this project.
        /// </summary>
        public IEnumerable<string> ReportsAvailableToParticipant { get; set; }

        /// <summary>
        /// Json containing commercial details for the project, including whether the project is full/self service
        /// </summary>
        public string CommercialJson
        {
            get => commercialJson;
            set
            {
                commercialJson = value;
                commercial = null;
            }
        }

        private string commercialJson;

        /// <summary>
        /// The commercial details for the project - this is the deserialised version of CommercialJson
        /// </summary>
        public ProjectHubCommercial Commercial => commercial ?? (commercial = CommercialJson == null ? null : JsonConvert.DeserializeObject<ProjectHubCommercial>(CommercialJson));

        private ProjectHubCommercial commercial;

        /// <summary>
        /// The list of all success profiles for the current project, including the original and additional ids, or null if this project doesn't support success profiles
        /// </summary>
        public IEnumerable<int> AllSuccessProfileIds
        {
            get
            {
                if (SuccessProfileId.HasValue)
                {
                    var ids = new List<int>(AdditionalSuccessProfileIds);
                    ids.Insert(0, SuccessProfileId.Value);
                    return ids;
                }

                return null;
            }
        }

        /// <summary>
        /// The source for the personality assessments within this project
        /// </summary>
        /// <remarks>Dimensions and KF4D are exclusively, so will never be mixed together</remarks>
        public AssessmentSource PersonalitySource
        {
            get
            {
                if (Assessments == null || Assessments.Behavioural == null)
                {
                    return AssessmentSource.Undefined;
                }
                else
                {
                    switch (Assessments.Behavioural.Type)
                    {
                        case AssessmentType.Dimensions:
                            return AssessmentSource.Dimensions;
                        case AssessmentType.EntryLevelCompetency:
                        case AssessmentType.VirtualRecruiter:
                            return AssessmentSource.ShortForm;
                        default:
                            return AssessmentSource.KF4D;
                    }
                }
            }
        }

        /// <summary>
        /// The source for the ability assessments within this project
        /// </summary>
        /// <remarks>Elements and Aspects are used exclusively, so will never be mixed together</remarks>
        public AssessmentSource AbilitySource
        {
            get
            {
                if (Assessments == null || !Assessments.AllToMeasure.Any())
                {
                    return AssessmentSource.Undefined;
                }
                else if (Assessments.AllToMeasure.Where(a => a.BlendedType.IsAbility()).All(a => a.Type.IsAspectsTypeTest()))
                {
                    return AssessmentSource.Aspects;
                }
                else if (Assessments.AllToMeasure.Where(a => a.BlendedType.IsAbility()).All(a => a.Type.IsElementsTest()))
                {
                    return AssessmentSource.Elements;
                }
                else if (Assessments.AllToMeasure.Where(a => a.BlendedType.IsAbility()).All(a => a.Type == AssessmentType.AbstractReasoning))
                {
                    return AssessmentSource.Ravens;
                }
                else
                {
                    return AssessmentSource.Mixed;
                }
            }
        }

        private Project project;

        /// <summary>
        /// The related KFAS Project
        /// </summary>
        public Project Project
        {
            get
            {
                if (project == null && ProjectId.HasValue)
                {
                    project = new Project(ProjectId.Value, 1);
                }

                return project;
            }

            set => project = value;
        }

        private Project parentProject;

        /// <summary>
        ///  The project details of the original project
        /// </summary>
        public Project ParentProject => parentProject ?? (parentProject = ParentProjectId.HasValue ? new Project(ParentProjectId.Value, 1) : null);

        private ProjectHub parentProjectHub;

        /// <summary>
        /// The projectHub for the parent project
        /// </summary>
        public ProjectHub ParentProjectHub => IsChildProject ? (parentProjectHub ?? (parentProjectHub = hubRepository.GetProjectHub(ParentProjectId.Value))) : null;

        /// <summary>
        /// Gets a value indicating whether this project contains ability assessments.
        /// </summary>
        public bool ContainsAbilityAssessments => Assessments.AllToMeasure.Any(a => a.Type.IsAbilityTest());

        /// <summary>
        /// Gets a value indicating whether this project contains the checking ability assessment.
        /// </summary>
        public bool ContainsCheckingAbilityAssessment => Assessments.AllToMeasure.Any(a => a.Type == AssessmentType.AspectsChecking);

        /// <summary>
        /// Gets a value indicating whether this project contains the competencies assessment, either Dimensions or KF4D.
        /// </summary>
        public bool ContainsCompetenciesAssessment => Assessments.AllToMeasure.Any(a => a.Type.ToBlendedAssessmentType() == BlendedAssessmentType.Competencies);

        /// <summary>
        /// Gets a value indicating whether this project contains the KF4D Traits assessment.
        /// </summary>
        public bool ContainsTraitsAssessment => Assessments.AllToMeasure.Any(a => a.Type == AssessmentType.Kf4dTraits);

        /// <summary>
        /// Gets a value indicating whether this project contains the KF4D Drivers assessment.
        /// </summary>
        public bool ContainsDriversAssessment => Assessments.AllToMeasure.Any(a => a.Type == AssessmentType.Kf4dDrivers);

        /// <summary>
        /// Gets a value indicating whether this project contains either Elements or Aspects Numerical ability assessment.
        /// </summary>
        public bool ContainsNumericalAbilityAssessment => Assessments.AllToMeasure.Any(a => a.Type.IsNumericalTest());

        /// <summary>
        /// Gets a value indicating whether this project contains either Elements or Aspects Verbal ability assessment.
        /// </summary>
        public bool ContainsVerbalAbilityAssessment => Assessments.AllToMeasure.Any(a => a.Type.IsVerbalTest());

        /// <summary>
        /// Gets a value indicating whether this project contains Elements logical assessment.
        /// </summary>
        public bool ContainsLogicalAbilityAssessment => Assessments.AllToMeasure.Any(a => a.Type == AssessmentType.ElementsLogical);

        /// <summary>
        /// Gets a value indicating whether blended scores are required for this project.
        /// </summary>
        public bool RequiresBlendedScores => SuccessProfileId.HasValue && SuccessProfileId.Value > 0 && ProjectType != ProjectHubType.SJT;

        /// <summary>
        /// Gets a value indicating whether scores relating to success profiles should be generated based on the project type.
        /// </summary>
        public bool HasSuccessProfileScoring => RequiresBlendedScores;

        /// <summary>
        /// Demographics type
        /// </summary>
        public BioDataType DemographicsType { get; set; }

        /// <summary>
        /// Demographics Config (only used if type is UnifiedV2)
        /// </summary>
        public int? DemographicsConfigId { get; set; }

        /// <summary>
        /// Id of report release notification template
        /// </summary>
        public int? ReportReleaseNotificationId { get; set; }

        /// <summary>
        /// Determines if the candidate can see any eLearning content from KF Learn on their dashboard
        /// </summary>
        public bool CandidateCanAccessLearningContent { get; set; }

        /// <summary>
        /// Determines which version of the participant portal, false for the current KF Assess 1 interface from KFAS.
        /// true to use the Employee Experience site from the IC2 platform for KF assess 2.
        /// </summary>
        public bool IsKFAssess2 { get; set; }

        /// <summary>
        /// Id of proctoring configuration record
        /// </summary>
        public int? ProctoringConfigurationId { get; set; }

        /// <summary>
        /// Name of ProctoringConfiguration for each project
        /// </summary>
        public string ProctoringConfigurationName { get; set; }

        /// <summary>
        /// Is proctoring enabled for the project
        /// </summary>
        public bool IsProctoringEnabled { get; set; }

        /// <summary>
        /// Is Proctoring Face Verification Stake selected for  project
        /// </summary>
        public bool IsFaceVerificationRequired { get; set; }

        /// <summary>
        /// Is secure browser enabled for a project
        /// </summary>
        public bool IsSecureBrowserEnabled { get; set; }

        /// <summary>
        /// Is Visible flag to hide/show the project in Products-Hub Assess and Select modules
        /// </summary>
        public bool Invisible { get; set; }

        private readonly IHubRepository hubRepository;

        /// <summary>
        /// Constructor for <see cref="ProjectHub"/>
        /// </summary>
        /// <param name="hubRepository">Optional <see cref="IHubRepository"/></param>
        public ProjectHub(IHubRepository hubRepository = null)
        {
            this.hubRepository = hubRepository ?? new HubDbRepository();
        }

        /// <summary>
        /// Gets the type of the KFAS assessment type for the given blended assessment type.
        /// </summary>
        /// <param name="blendedAssessmentType">Type of the blended assessment.</param>
        /// <returns>The assessment type or AssessmentType.None if the assessment is not found.</returns>
        public AssessmentType GetAssessmentType(BlendedAssessmentType blendedAssessmentType)
        {
            var assessment = Assessments.AllToMeasure.FirstOrDefault(a => a.Type.ToBlendedAssessmentType() == blendedAssessmentType);
            return assessment?.Type ?? AssessmentType.None;
        }
    }

    /// <summary>
    /// The fit target scores for a <see cref="ProjectHub"/>
    /// </summary>
    public class ProjectHubTargets
    {
        /// <summary>
        /// The static target for the Drivers assessment
        /// </summary>
        internal const int DriversTarget = 6;

        /// <summary>
        /// A default/empty <see cref="ProjectHubTargets"/>
        /// </summary>
        public ProjectHubTargets()
        {
        }

        /// <summary>
        /// Creates a <see cref="ProjectHubTargets"/> populated with values derived from <paramref name="assessments"/>
        /// </summary>
        /// <param name="assessments">The assessments containing target information</param>
        public ProjectHubTargets(ProjectHubAssessments assessments)
        {
            if (assessments != null)
            {
                var abilityCount = assessments.AllToMeasure.Count(a => a.BlendedType.IsAbility());
                Ability = abilityCount == 0 ? (int?)null : abilityCount;
                Competencies = (assessments.Behavioural?.Measure ?? false) ? assessments.Behavioural.Report?.Count : null;
                Traits = (assessments.Traits?.Measure ?? false) ? assessments.Traits.Report?.Count : null;
                Drivers = (assessments.Drivers?.Measure ?? false) ? DriversTarget : (int?)null;
            }
        }

        /// <summary>
        /// The competencies target
        /// </summary>
        public int? Competencies { get; set; }

        /// <summary>
        /// The Traits target
        /// </summary>
        public int? Traits { get; set; }

        /// <summary>
        /// The Drivers target
        /// </summary>
        public int? Drivers { get; set; }

        /// <summary>
        /// The Ability target
        /// </summary>
        public int? Ability { get; set; }
    }

    /// <summary>
    /// General project settings
    /// </summary>
    public class ProjectSettings
    {
        /// <summary>
        /// Flag to determine if the project reported competency, traits and driver scales should be used
        /// instead of all scales in reports that currently list all scales, e.g. development report always includes
        /// all scales regardless of what's configured in the project. e.g. when true the development report will match the scales
        /// currently in the hiring manager report.
        /// </summary>
        public bool ProjectScalesOnly { get; set; }
    }

    /// <summary>
    /// General Project Hub metrics
    /// </summary>
    public class ProjectMetrics
    {
        /// <summary>
        /// Number of candidates who started but not completed their assessments
        /// </summary>
        public int? CandidatesInProgress { get; set; }

        /// <summary>
        /// Number of candidates who completed their assessments
        /// </summary>
        public int? CandidatesCompleted { get; set; }

        /// <summary>
        /// Indicates there are no null properties at all
        /// </summary>
        public bool HasValues => CandidatesInProgress.HasValue && CandidatesCompleted.HasValue;

        /// <summary>
        /// Indicates if there are participants in progress or completed
        /// </summary>
        public bool HasStartedParticipants => HasValues && (CandidatesInProgress > 0 || CandidatesCompleted > 0);

        /// <summary>
        /// Total number of candidates in project
        /// </summary>
        public int? TotalParticipants { get; set; }
    }
}