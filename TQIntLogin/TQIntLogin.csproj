﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{A323BB12-502F-4526-B876-0FDF15C4DA4C}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TQIntLogin</RootNamespace>
    <AssemblyName>TQIntLogin</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>false</UseIISExpress>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>4.0</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <MvcProjectUpgradeChecked>true</MvcProjectUpgradeChecked>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <UseGlobalApplicationHostFile />
    <TargetFrameworkProfile />
    <Use64BitIISExpress />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Deploy|AnyCPU' ">
    <Optimize>true</Optimize>
    <PublishDatabases>false</PublishDatabases>
    <DeployIisAppPath>testlogin.talentqgroup.com</DeployIisAppPath>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <Optimize>true</Optimize>
    <PublishDatabases>false</PublishDatabases>
    <DeployIisAppPath>login.talentqgroup.com</DeployIisAppPath>
    <OutputPath>bin\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <Optimize>false</Optimize>
    <PublishDatabases>false</PublishDatabases>
    <DeployIisAppPath>locallogin.talentqgroup.com</DeployIisAppPath>
    <OutputPath>bin\</OutputPath>
    <DebugType>full</DebugType>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Deploy|x86' ">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Deploy|x64' ">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Content|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Content|x86'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Content|x64'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Code|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Code|x86'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Code|x64'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'CAS|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'CAS|x86'">
    <OutputPath>bin\</OutputPath>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'CAS|x64'">
    <OutputPath>bin\</OutputPath>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Deploy|AnyCPU' ">
    <Optimize>true</Optimize>
    <PublishDatabases>false</PublishDatabases>
    <DeployIisAppPath>testlogin.talentqgroup.com</DeployIisAppPath>
    <OutputPath>bin\</OutputPath>
    <Prefer32Bit>false</Prefer32Bit>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <Optimize>true</Optimize>
    <PublishDatabases>false</PublishDatabases>
    <DeployIisAppPath>login.talentqgroup.com</DeployIisAppPath>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Prefer32Bit>false</Prefer32Bit>
    <CodeAnalysisRuleSet>..\TQASStylecop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <Optimize>false</Optimize>
    <PublishDatabases>false</PublishDatabases>
    <DeployIisAppPath>locallogin.talentqgroup.com</DeployIisAppPath>
    <OutputPath>bin\</OutputPath>
    <DebugType>full</DebugType>
    <Prefer32Bit>false</Prefer32Bit>
    <CodeAnalysisRuleSet>..\TQASStylecop.ruleset</CodeAnalysisRuleSet>
    <DebugSymbols>false</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Deploy|x86' ">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Deploy|x64' ">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>..\Stylecop.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime">
      <HintPath>..\packages\Antlr.3.4.1.9004\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="AWS.Logger.Core, Version=3.1.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWS.Logger.Core.3.1.0\lib\net45\AWS.Logger.Core.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.CloudWatchLogs, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWSSDK.CloudWatchLogs.********\lib\net45\AWSSDK.CloudWatchLogs.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.Core, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWSSDK.Core.3.7.11.4\lib\net45\AWSSDK.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.7.15\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="NLog.AWS.Logger, Version=3.1.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWS.Logger.NLog.3.1.0\lib\net45\NLog.AWS.Logger.dll</HintPath>
    </Reference>
    <Reference Include="NLog.Extended, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.Extended.4.7.15\lib\net45\NLog.Extended.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Entity" />
    <Reference Include="System.IO.Compression, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="System.Messaging" />
    <Reference Include="System.Net.Http, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.2\lib\net45\System.Web.Helpers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.2\lib\net45\System.Web.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.2\lib\net45\System.Web.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.2\lib\net45\System.Web.WebPages.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.2\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.2\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WebGrease">
      <HintPath>..\packages\WebGrease.1.5.2\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Wurfl, Version=1.9.0.1, Culture=neutral, PublicKeyToken=816aeec277aa13b9, processorArchitecture=MSIL">
      <HintPath>..\packages\WURFL_Official_API.1.9.0.1\lib\Wurfl.dll</HintPath>
    </Reference>
    <Reference Include="Wurfl.Aspnet.Extensions, Version=1.9.0.0, Culture=neutral, PublicKeyToken=816aeec277aa13b9, processorArchitecture=MSIL">
      <HintPath>..\packages\WURFL_Official_API.1.9.0.1\lib\Wurfl.Aspnet.Extensions.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Controllers\BrandingController.cs" />
    <Compile Include="Controllers\DataProtectionSessionState.cs" />
    <Compile Include="Controllers\LoginController.cs">
      <ExcludeFromStyleCop>False</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Controllers\LoginModeState.cs" />
    <Compile Include="Controllers\MyTalentQController.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Helpers\DebugHelper.cs" />
    <Compile Include="Helpers\HtmlExtensions.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\DataProtectionModel.cs" />
    <Compile Include="Models\UnHandledErrorModel.cs" />
    <Compile Include="Models\TermsAndConditionsModel.cs" />
    <Compile Include="Models\CompleteModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\CitiSelfServiceModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\ErrorModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\LoginModel.cs">
      <ExcludeFromStyleCop>False</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Models\SelfServiceModel.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs">
      <ExcludeFromStyleCop>True</ExcludeFromStyleCop>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="appstatus.html" />
    <Content Include="App_Readme\Readme.txt" />
    <Content Include="browserconfig.xml" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\fonts\bootstrap\glyphicons-halflings-regular.svg" />
    <Content Include="Content\Images\Candidate\bg_arrow-darkblue-bluebg-left.png" />
    <Content Include="Content\Images\Candidate\bg_arrow-darkblue-bluebg.png" />
    <Content Include="Content\Images\Candidate\bg_arrow-darkblue-rtl.png" />
    <Content Include="Content\Images\Candidate\bg_arrow-darkblue.png" />
    <Content Include="Content\Images\Candidate\bg_arrow-grey.png" />
    <Content Include="Content\Images\Candidate\bg_arrow-red.png" />
    <Content Include="Content\Images\Candidate\bg_arrow-white-rtl.png" />
    <Content Include="Content\Images\Candidate\bg_arrow-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\action-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\alert-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\arrow-d-l-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\arrow-d-r-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\arrow-d-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\arrow-l-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\arrow-r-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\arrow-u-l-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\arrow-u-r-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\arrow-u-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\audio-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\back-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\bars-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\bullets-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\calendar-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\camera-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\carat-d-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\carat-l-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\carat-r-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\carat-u-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\check-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\clock-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\cloud-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\comment-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\delete-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\edit-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\eye-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\forbidden-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\forward-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\gear-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\grid-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\heart-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\home-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\info-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\location-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\lock-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\mail-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\minus-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\navigation-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\phone-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\plus-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\power-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\recycle-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\refresh-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\search-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\shop-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\star-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\tag-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\user-white.png" />
    <Content Include="Content\Images\Candidate\Mobile\jQueryMobile\video-white.png" />
    <Content Include="Content\Images\Citi\BanamexLogo.png" />
    <Content Include="Content\Images\Citi\citi.css" />
    <Content Include="Content\Images\Citi\CitiBlueWave.png" />
    <Content Include="Content\Images\Citi\CitiLogo.png" />
    <Content Include="Content\Images\Loading\ajax-loader.gif" />
    <Content Include="Content\Images\Login\arrow-left.png" />
    <Content Include="Content\Images\Login\arrow-right.png" />
    <Content Include="Content\Images\Login\changepasswordbg.png" />
    <Content Include="Content\Images\Login\kflogo.jpg" />
    <Content Include="Content\Images\Login\loginbg.png" />
    <Content Include="Content\Images\Login\tqas.png" />
    <Content Include="Content\Images\Login\tqbanner.png" />
    <Content Include="Content\Images\Login\tqlogo.png" />
    <Content Include="Content\Images\Responsive\hero-large.jpg" />
    <Content Include="Content\Images\Responsive\hero-small.jpg" />
    <Content Include="Content\Images\Responsive\KFlogoDark.png" />
    <Content Include="Content\Images\Responsive\KFlogoLight.png" />
    <Content Include="Content\Images\Responsive\KornFerryLogo.png" />
    <Content Include="Content\Images\Responsive\KornFerryLogoDark.png" />
    <Content Include="Content\Images\Responsive\KornFerryLogoLight.png" />
    <Content Include="Content\Styles\BrandingTemplates\BrandingTemplateLoginMobile.css" />
    <Content Include="Content\Styles\BrandingTemplates\BrandingTemplateLoginNew.css" />
    <Content Include="Content\Styles\BrandingBase.css" />
    <Content Include="Content\Styles\BrandingTemplates\BrandingTemplateSelfService.css" />
    <Content Include="Content\Styles\Branding\donotdelete.css" />
    <Content Include="Content\Styles\FontAwesome\font-awesome-ie7.min.css" />
    <Content Include="Content\Styles\FontAwesome\font-awesome.min.css" />
    <Content Include="Content\Styles\FontAwesome\font\fontawesome-webfont.svg" />
    <Content Include="Content\Styles\LoginBundle.css" />
    <Content Include="Content\Styles\LoginBundle.min.css">
      <DependentUpon>LoginBundle.css</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Mobile\jquery.mobile-1.4.0-rc.1.css" />
    <Content Include="Content\Styles\Mobile\jquery.mobile.structure-1.4.0-rc.1.min.css" />
    <Content Include="Content\Styles\Mobile\TalentQ.min.css" />
    <Content Include="Content\Styles\Mobile\TQMobile.css" />
    <Content Include="Content\Styles\Mobile\TQRightToLeft.css" />
    <Content Include="Content\Styles\Responsive\main-apac.css">
      <DependentUpon>main-apac.scss</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Responsive\main-apac.min.css">
      <DependentUpon>main-apac.css</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Responsive\main.css">
      <DependentUpon>main.scss</DependentUpon>
    </Content>
    <Content Include="Content\Styles\Responsive\main.min.css">
      <DependentUpon>main.css</DependentUpon>
    </Content>
    <Content Include="Content\Styles\TQReset.css" />
    <Content Include="Content\Styles\TQForms.css" />
    <Content Include="Content\Styles\TQLayout.css" />
    <Content Include="Content\Styles\TQLogin.css" />
    <Content Include="Content\Styles\TQRightToLeft.css" />
    <Content Include="Content\Styles\TQType.css" />
    <Content Include="Content\Themes\base\images\ui-bg_flat_0_aaaaaa_40x100.png" />
    <Content Include="Content\Themes\base\images\ui-bg_flat_75_ffffff_40x100.png" />
    <Content Include="Content\Themes\base\images\ui-bg_glass_55_fbf9ee_1x400.png" />
    <Content Include="Content\Themes\base\images\ui-bg_glass_65_ffffff_1x400.png" />
    <Content Include="Content\Themes\base\images\ui-bg_glass_75_dadada_1x400.png" />
    <Content Include="Content\Themes\base\images\ui-bg_glass_75_e6e6e6_1x400.png" />
    <Content Include="Content\Themes\base\images\ui-bg_glass_95_fef1ec_1x400.png" />
    <Content Include="Content\Themes\base\images\ui-bg_highlight-soft_75_cccccc_1x100.png" />
    <Content Include="Content\Themes\base\images\ui-icons_222222_256x240.png" />
    <Content Include="Content\Themes\base\images\ui-icons_2e83ff_256x240.png" />
    <Content Include="Content\Themes\base\images\ui-icons_454545_256x240.png" />
    <Content Include="Content\Themes\base\images\ui-icons_888888_256x240.png" />
    <Content Include="Content\Themes\base\images\ui-icons_cd0a0a_256x240.png" />
    <Content Include="Content\Themes\base\jquery.ui.accordion.css" />
    <Content Include="Content\Themes\base\jquery.ui.all.css" />
    <Content Include="Content\Themes\base\jquery.ui.autocomplete.css" />
    <Content Include="Content\Themes\base\jquery.ui.base.css" />
    <Content Include="Content\Themes\base\jquery.ui.button.css" />
    <Content Include="Content\Themes\base\jquery.ui.core.css" />
    <Content Include="Content\Themes\base\jquery.ui.datepicker.css" />
    <Content Include="Content\Themes\base\jquery.ui.dialog.css" />
    <Content Include="Content\Themes\base\jquery.ui.progressbar.css" />
    <Content Include="Content\Themes\base\jquery.ui.resizable.css" />
    <Content Include="Content\Themes\base\jquery.ui.selectable.css" />
    <Content Include="Content\Themes\base\jquery.ui.slider.css" />
    <Content Include="Content\Themes\base\jquery.ui.tabs.css" />
    <Content Include="Content\Themes\base\jquery.ui.theme.css" />
    <Content Include="Content\vendor\bootstrap\scss\tests\jasmine.js" />
    <Content Include="Content\vendor\bootstrap\scss\tests\sass-true\register.js" />
    <Content Include="Content\vendor\bootstrap\scss\tests\sass-true\runner.js" />
    <Content Include="Fileshare\InterfaceBranding\donotremove.txt" />
    <Content Include="Global.asax" />
    <Content Include="robots.txt" />
    <Content Include="App_Code\TQControls.cshtml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="NLog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TransformOnBuild>false</TransformOnBuild>
    </Content>
    <Content Include="Content\Styles\FontAwesome\font\fontawesome-webfont.eot" />
    <Content Include="Content\Styles\FontAwesome\font\fontawesome-webfont.ttf" />
    <Content Include="Content\Styles\FontAwesome\font\fontawesome-webfont.woff" />
    <Content Include="Content\Styles\FontAwesome\font\FontAwesome.otf" />
    <Content Include="bundleconfig.json" />
    <Content Include="Content\Styles\Responsive\_bootstrap-compass.scss" />
    <Content Include="Content\Styles\Responsive\_bootstrap-mincer.scss" />
    <Content Include="Content\Styles\Responsive\_bootstrap-sprockets.scss" />
    <Content Include="Content\Styles\Responsive\_bootstrap.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_alerts.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_badges.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_breadcrumbs.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_button-groups.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_buttons.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_carousel.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_close.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_code.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_component-animations.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_dropdowns.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_forms.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_glyphicons.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_grid.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_input-groups.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_jumbotron.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_labels.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_list-group.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_media.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_mixins.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_modals.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_navbar.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_navs.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_normalize.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_pager.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_pagination.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_panels.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_popovers.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_print.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_progress-bars.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_responsive-embed.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_responsive-utilities.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_scaffolding.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_tables.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_theme.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_thumbnails.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_tooltip.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_type.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_utilities.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_variables.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\_wells.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_alerts.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_background-variant.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_border-radius.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_buttons.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_center-block.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_clearfix.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_forms.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_gradients.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_grid-framework.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_grid.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_hide-text.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_image.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_labels.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_list-group.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_nav-divider.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_nav-vertical-align.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_opacity.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_pagination.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_panels.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_progress-bar.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_reset-filter.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_reset-text.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_resize.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_responsive-visibility.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_size.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_tab-focus.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_table-row.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_text-emphasis.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_text-overflow.scss" />
    <Content Include="Content\Styles\Responsive\bootstrap\mixins\_vendor-prefixes.scss" />
    <Content Include="Content\Styles\Responsive\common\_common.scss" />
    <Content Include="Content\Styles\Responsive\common\_fonts.scss" />
    <Content Include="Content\Styles\Responsive\common\_forms.scss" />
    <Content Include="Content\Styles\Responsive\common\_icons.scss" />
    <Content Include="Content\Styles\Responsive\common\_mixins.scss" />
    <Content Include="Content\Styles\Responsive\common\_redefine.scss" />
    <Content Include="Content\Styles\Responsive\common\_typography.scss" />
    <Content Include="Content\Styles\Responsive\common\_variables.scss" />
    <Content Include="Content\Styles\Responsive\_layout.scss" />
    <Content Include="Content\Styles\Responsive\main.scss" />
    <Content Include="Content\fonts\bootstrap\glyphicons-halflings-regular.eot" />
    <Content Include="Content\fonts\bootstrap\glyphicons-halflings-regular.ttf" />
    <Content Include="Content\fonts\bootstrap\glyphicons-halflings-regular.woff" />
    <Content Include="Content\fonts\bootstrap\glyphicons-halflings-regular.woff2" />
    <Content Include="Content\fonts\proximanova\3122C9_0_0.eot" />
    <Content Include="Content\fonts\proximanova\3122C9_0_0.ttf" />
    <Content Include="Content\fonts\proximanova\3122C9_0_0.woff" />
    <Content Include="Content\fonts\proximanova\3122C9_0_0.woff2" />
    <Content Include="Content\fonts\proximanova\3122C9_1_0.eot" />
    <Content Include="Content\fonts\proximanova\3122C9_1_0.ttf" />
    <Content Include="Content\fonts\proximanova\3122C9_1_0.woff" />
    <Content Include="Content\fonts\proximanova\3122C9_1_0.woff2" />
    <Content Include="Content\fonts\proximanova\3122C9_2_0.eot" />
    <Content Include="Content\fonts\proximanova\3122C9_2_0.ttf" />
    <Content Include="Content\fonts\proximanova\3122C9_2_0.woff" />
    <Content Include="Content\fonts\proximanova\3122C9_2_0.woff2" />
    <Content Include="Content\fonts\proximanova\3122C9_3_0.eot" />
    <Content Include="Content\fonts\proximanova\3122C9_3_0.ttf" />
    <Content Include="Content\fonts\proximanova\3122C9_3_0.woff" />
    <Content Include="Content\fonts\proximanova\3122C9_3_0.woff2" />
    <Content Include="Content\fonts\proximanova\3122C9_4_0.eot" />
    <Content Include="Content\fonts\proximanova\3122C9_4_0.ttf" />
    <Content Include="Content\fonts\proximanova\3122C9_4_0.woff" />
    <Content Include="Content\fonts\proximanova\3122C9_4_0.woff2" />
    <Content Include="Content\fonts\proximanova\3122C9_5_0.eot" />
    <Content Include="Content\fonts\proximanova\3122C9_5_0.ttf" />
    <Content Include="Content\fonts\proximanova\3122C9_5_0.woff" />
    <Content Include="Content\fonts\proximanova\3122C9_5_0.woff2" />
    <Content Include="App_Data\wurfl.zip" />
    <Content Include="App_Readme\WURFL.chm" />
    <Content Include="App_Readme\wurfl-latest.zip" />
    <None Include="compilerconfig.json" />
    <None Include="compilerconfig.json.defaults">
      <DependentUpon>compilerconfig.json</DependentUpon>
    </None>
    <Content Include="Content\Styles\Responsive\_rtl.scss" />
    <Content Include="Content\Styles\Responsive\main-apac.scss" />
    <Content Include="NLog.Release.config">
      <DependentUpon>NLog.config</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Introspector\Introspector.pdf" />
    <Content Include="Introspector\Introspector.ashx" />
    <Content Include="COPYING.evaluation" />
    <Content Include="Content\vendor\bootstrap\scss\bootstrap-grid.scss" />
    <Content Include="Content\vendor\bootstrap\scss\bootstrap-reboot.scss" />
    <Content Include="Content\vendor\bootstrap\scss\bootstrap-utilities.scss" />
    <Content Include="Content\vendor\bootstrap\scss\bootstrap.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_floating-labels.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_form-check.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_form-control.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_form-range.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_form-select.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_form-text.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_input-group.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_labels.scss" />
    <Content Include="Content\vendor\bootstrap\scss\forms\_validation.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_clearfix.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_color-bg.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_colored-links.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_focus-ring.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_icon-link.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_position.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_ratio.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_stacks.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_stretched-link.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_text-truncation.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_visually-hidden.scss" />
    <Content Include="Content\vendor\bootstrap\scss\helpers\_vr.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_alert.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_backdrop.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_banner.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_border-radius.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_box-shadow.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_breakpoints.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_buttons.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_caret.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_clearfix.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_color-mode.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_color-scheme.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_container.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_deprecate.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_forms.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_gradients.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_grid.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_image.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_list-group.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_lists.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_pagination.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_reset-text.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_resize.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_table-variants.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_text-truncate.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_transition.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_utilities.scss" />
    <Content Include="Content\vendor\bootstrap\scss\mixins\_visually-hidden.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\mixins\_auto-import-of-variables-dark.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\mixins\_box-shadow.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\mixins\_color-contrast.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\mixins\_color-modes.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\mixins\_media-query-color-mode-full.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\mixins\_utilities.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\tests\utilities\_api.test.scss" />
    <Content Include="Content\vendor\bootstrap\scss\utilities\_api.scss" />
    <Content Include="Content\vendor\bootstrap\scss\vendor\_rfs.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_accordion.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_alert.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_badge.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_breadcrumb.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_button-group.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_buttons.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_card.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_carousel.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_close.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_containers.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_dropdown.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_forms.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_functions.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_grid.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_helpers.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_images.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_list-group.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_maps.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_mixins.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_modal.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_nav.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_navbar.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_offcanvas.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_pagination.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_placeholders.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_popover.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_progress.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_reboot.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_root.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_spinners.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_tables.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_toasts.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_tooltip.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_transitions.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_type.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_utilities.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_variables-dark.scss" />
    <Content Include="Content\vendor\bootstrap\scss\_variables.scss" />
    <None Include="NLog.Staging.config">
      <DependentUpon>NLog.config</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="NLog.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\PublishProfiles\Local.pubxml" />
    <None Include="Scripts\jquery-1.5.1-vsdoc.js" />
    <Content Include="Scripts\bootstrap-sprockets.js" />
    <Content Include="Scripts\bootstrap.bundle.min.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\affix.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\alert.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\button.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\carousel.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\collapse.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\dropdown.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\modal.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\popover.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\scrollspy.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\tab.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\tooltip.js" />
    <Content Include="Scripts\bootstrap-v3.3.6\transition.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap\alert.js" />
    <Content Include="Scripts\bootstrap\button.js" />
    <Content Include="Scripts\bootstrap\carousel.js" />
    <Content Include="Scripts\bootstrap\collapse.js" />
    <Content Include="Scripts\bootstrap\dropdown.js" />
    <Content Include="Scripts\bootstrap\modal.js" />
    <Content Include="Scripts\bootstrap\popover.js" />
    <Content Include="Scripts\bootstrap\scrollspy.js" />
    <Content Include="Scripts\bootstrap\tab.js" />
    <Content Include="Scripts\bootstrap\tooltip.js" />
    <Content Include="Scripts\jquery-1.5.1.js" />
    <Content Include="Scripts\jquery-1.5.1.min.js" />
    <None Include="Scripts\jquery-1.9.1-vsdoc.js" />
    <Content Include="Scripts\jquery-1.9.1.min.js" />
    <Content Include="Scripts\jquery-ui-1.8.11.js" />
    <Content Include="Scripts\jquery-ui-1.8.11.min.js" />
    <Content Include="Scripts\jquery.mobile-1.4.0-rc.1.js" />
    <Content Include="Scripts\jquery.unobtrusive-ajax.js" />
    <Content Include="Scripts\jquery.unobtrusive-ajax.min.js" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Scripts\LoginBundle.js" />
    <Content Include="Scripts\LoginBundle.min.js">
      <DependentUpon>LoginBundle.js</DependentUpon>
    </Content>
    <Content Include="Scripts\Mobile\jquery.mobile-1.4.0-rc.1.js" />
    <Content Include="Scripts\Mobile\jquery.mobile-1.4.0-rc.1.min.js" />
    <Content Include="Scripts\Mobile\TQMobile.js" />
    <Content Include="Scripts\modernizr-1.7.js" />
    <Content Include="Scripts\modernizr-1.7.min.js" />
    <Content Include="Scripts\PIE.htc" />
    <Content Include="Scripts\Responsive\LoginResponsive.js" />
    <Content Include="Scripts\Responsive\LoginResponsive.min.js">
      <DependentUpon>LoginResponsive.js</DependentUpon>
    </Content>
    <Content Include="Scripts\TQBrandingEditor.js" />
    <Content Include="Scripts\TQDataProtection.js" />
    <Content Include="Scripts\Responsive\TQDefault.js" />
    <Content Include="Scripts\TQDefault.js" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Views\Login\Complete.cshtml" />
    <Content Include="Views\Login\CitiSelfService.cshtml" />
    <Content Include="Views\Shared\Mobile\_BrandedContent.cshtml" />
    <Content Include="Views\Shared\Mobile\_BrandedContent.cshtml" />
    <Content Include="Web.CdnHeaders.config" />
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\packages\AWSSDK.CloudWatchLogs.********\analyzers\dotnet\cs\AWSSDK.CloudWatchLogs.CodeAnalysis.dll" />
    <Analyzer Include="..\packages\StyleCop.Analyzers.1.0.0\analyzers\dotnet\cs\Newtonsoft.Json.dll" />
    <Analyzer Include="..\packages\StyleCop.Analyzers.1.0.0\analyzers\dotnet\cs\StyleCop.Analyzers.CodeFixes.dll" />
    <Analyzer Include="..\packages\StyleCop.Analyzers.1.0.0\analyzers\dotnet\cs\StyleCop.Analyzers.dll" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Login\SelfService.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Login\Login.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\TalentQ.Integrations\TalentQ.Integrations.csproj">
      <Project>{d5634814-e0d7-475e-8538-01efbd2bbc4f}</Project>
      <Name>TalentQ.Integrations</Name>
    </ProjectReference>
    <ProjectReference Include="..\TQCommon\TQCommon.csproj">
      <Project>{E473FCAA-4E5F-4AED-A7FC-3AD6784BA49A}</Project>
      <Name>TQCommon</Name>
    </ProjectReference>
    <ProjectReference Include="..\TQIntShared\TQIntShared.csproj">
      <Project>{94710873-327C-441A-B6E5-1F3B91601A2E}</Project>
      <Name>TQIntShared</Name>
    </ProjectReference>
    <ProjectReference Include="..\TQLogic\TQLogic.csproj">
      <Project>{1398EA65-2390-477E-8ACA-0AF976F077FB}</Project>
      <Name>TQLogic</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Shared\_Footer.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Shared\_Layout.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_Code\TQContent.cshtml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <None Include="Properties\PublishProfiles\Release.pubxml" />
    <None Include="Properties\PublishProfiles\Staging.pubxml" />
    <Content Include="Views\Login\Error.cshtml" />
    <Content Include="Views\Login\Mobile\Error.cshtml" />
    <Content Include="Views\Login\Mobile\Login.cshtml" />
    <Content Include="Views\Login\Mobile\SelfService.cshtml" />
    <Content Include="Views\Shared\Mobile\_Footer.cshtml" />
    <Content Include="Views\Shared\Mobile\_Layout.cshtml" />
    <Content Include="Views\Login\TermsAndConditions.cshtml" />
    <Content Include="Views\Login\DataProtection.cshtml" />
    <Content Include="Views\Login\Responsive\Complete.cshtml" />
    <Content Include="Views\Login\Responsive\DataProtection.cshtml" />
    <Content Include="Views\Login\Responsive\Error.cshtml" />
    <Content Include="Views\Login\Responsive\Login.cshtml" />
    <Content Include="Views\Login\Responsive\SelfService.cshtml" />
    <Content Include="Views\Login\Responsive\TermsAndConditions.cshtml" />
    <Content Include="Views\Shared\Responsive\_Layout.cshtml" />
    <Content Include="Views\Shared\Responsive\_Footer.cshtml" />
    <Content Include="Views\Shared\Responsive\_Header.cshtml" />
    <Content Include="Views\Login\Responsive\_LoginForm.cshtml" />
    <Content Include="Views\Login\Responsive\_PasswordResetRequest.cshtml" />
    <Content Include="Views\Login\Responsive\_PasswordReset.cshtml" />
    <Content Include="Views\Login\Responsive\_ForcePasswordReset.cshtml" />
    <Content Include="Views\Branding\Responsive\Styles.cshtml" />
    <Content Include="Views\Branding\Styles.cshtml" />
    <Content Include="Views\Login\Responsive\UnhandledError.cshtml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.4.5">
      <Visible>False</Visible>
      <ProductName>Windows Installer 4.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup />
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <ProjectGuid>{A323BB12-502F-4526-B876-0FDF15C4DA4C}</ProjectGuid>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Label="SlowCheetah">
    <SlowCheetahToolsPath>$([System.IO.Path]::GetFullPath( $(MSBuildProjectDirectory)\..\packages\SlowCheetah.2.5.10.3\tools\))</SlowCheetahToolsPath>
    <SlowCheetah_EnableImportFromNuGet Condition=" '$(SC_EnableImportFromNuGet)'=='' ">true</SlowCheetah_EnableImportFromNuGet>
    <SlowCheetah_NuGetImportPath Condition=" '$(SlowCheetah_NuGetImportPath)'=='' ">$([System.IO.Path]::GetFullPath( $(MSBuildProjectDirectory)\Properties\SlowCheetah\SlowCheetah.Transforms.targets ))</SlowCheetah_NuGetImportPath>
    <SlowCheetahTargets Condition=" '$(SlowCheetah_EnableImportFromNuGet)'=='true' and Exists('$(SlowCheetah_NuGetImportPath)') ">$(SlowCheetah_NuGetImportPath)</SlowCheetahTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>64340</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://locallogin.talentqgroup.com</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>https://locallogin.talentqgroup.com</CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="$(SlowCheetahTargets)" Condition="Exists('$(SlowCheetahTargets)')" Label="SlowCheetah" />
  <Import Project="..\packages\OctoPack.3.0.44\tools\OctoPack.targets" Condition="Exists('..\packages\OctoPack.3.0.44\tools\OctoPack.targets')" />
  <Target Name="EnsureOctoPackImported" BeforeTargets="BeforeBuild" Condition="'$(OctoPackImported)' == ''">
    <Error Condition="!Exists('..\packages\OctoPack.3.0.44\tools\OctoPack.targets') And ('$(RunOctoPack)' != '' And $(RunOctoPack))" Text="You are trying to build with OctoPack, but the NuGet targets file that OctoPack depends on is not available on this computer. This is probably because the OctoPack package has not been committed to source control, or NuGet Package Restore is not enabled. Please enable NuGet Package Restore to download them. For more information, see http://go.microsoft.com/fwlink/?LinkID=317567." HelpKeyword="BCLBUILD2001" />
    <Error Condition="Exists('..\packages\OctoPack.3.0.44\tools\OctoPack.targets') And ('$(RunOctoPack)' != '' And $(RunOctoPack))" Text="OctoPack cannot be run because NuGet packages were restored prior to the build running, and the targets file was unavailable when the build started. Please build the project again to include these packages in the build. You may also need to make sure that your build server does not delete packages prior to each build. For more information, see http://go.microsoft.com/fwlink/?LinkID=317568." HelpKeyword="BCLBUILD2002" />
  </Target>
  <Import Project="..\packages\BuildWebCompiler.1.11.315\build\BuildWebCompiler.targets" Condition="Exists('..\packages\BuildWebCompiler.1.11.315\build\BuildWebCompiler.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\BuildWebCompiler.1.11.315\build\BuildWebCompiler.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\BuildWebCompiler.1.11.315\build\BuildWebCompiler.targets'))" />
  </Target>
</Project>