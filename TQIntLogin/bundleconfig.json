[{"outputFileName": "Scripts/LoginBundle.js", "inputFiles": ["Scripts/jquery-1.9.1.min.js", "Scripts/jquery.validate.min.js", "Scripts/jquery.validate.unobtrusive.min.js", "Scripts/TQDefault.js"]}, {"outputFileName": "Content/Styles/LoginBundle.css", "inputFiles": ["Content/Styles/TQForms.css", "Content/Styles/TQLayout.css", "Content/Styles/TQLogin.css", "Content/Styles/TQReset.css", "Content/Styles/TQType.css", "Content/Styles/FontAwesome/font-awesome.min.css"]}, {"outputFileName": "Scripts/Responsive/LoginResponsive.js", "inputFiles": ["Scripts/jquery-1.9.1.min.js", "Scripts/jquery.validate.min.js", "Scripts/jquery.validate.unobtrusive.min.js", "Scripts/bootstrap.bundle.min.js", "Scripts/Responsive/TQDefault.js"]}, {"outputFileName": "Scripts/Responsive/TQDataProtection.min.js", "inputFiles": ["Scripts/Responsive/TQDataProtection.js"]}, {"outputFileName": "Content/Styles/Responsive/main.min.min.css", "inputFiles": ["Content/Styles/Responsive/main.min.css"]}]