﻿using KornFerry.Services.Content;
using KornFerry.Services.Extracts.Service;
using KornFerry.Services.Scoring.Shared.Storage;
using KornFerry.Services.Scoring.Shared.Storage.Models;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using TalentQ.Integrations.KFATS.Common.Models;
using TalentQ.Integrations.KFATS.Common.Results;
using TalentQ.Utilities;
using TQLogic.Assessments;
using TQLogic.Projects.Hub;

namespace TalentQ.Integrations.KFATS.Hub.Results
{
    /// <summary>
    /// Results builder for fit scores / blended scores / standard SP scores
    /// </summary>
    internal class FitScoreBuilder : IScoreBuilder
    {
        private static readonly Logger log = LogManager.GetCurrentClassLogger();
        private readonly Lazy<ScoreResult> scores;
        private readonly int externalClientId;
        private readonly int successProfileId;
        private readonly ProjectHubAssessments projectAssessments;
        private readonly ICommonTextLookup textLookup;

        /// <summary>
        /// Map between an ability blended assessment type and a storage score key
        /// </summary>
        internal static readonly Dictionary<BlendedAssessmentType, string> AbilityBlendedTypeScoreKey = new Dictionary<BlendedAssessmentType, string>
        {
            [BlendedAssessmentType.Numerical] = ScoreKey.Numerical,
            [BlendedAssessmentType.Verbal] = ScoreKey.Verbal,
            [BlendedAssessmentType.Logical] = ScoreKey.Logical,
            [BlendedAssessmentType.Checking] = ScoreKey.Checking
        };

        /// <summary>
        /// A new <see cref="FitScoreBuilder"/>
        /// </summary>
        /// <param name="candidateId">Id of the candidate to build results for</param>
        /// <param name="projectId">Id of the project to build results for</param>
        /// <param name="externalClientId">External client Id of the client that the <paramref name="candidateId"/> and <paramref name="projectId"/> belong to</param>
        /// <param name="successProfileId">Id of the success profile that is being scored</param>
        /// <param name="projectAssessments">The assessments related to the project</param>
        /// <param name="scoreExtract">The score extract service</param>
        /// <param name="textLookup">The text lookup service</param>
        public FitScoreBuilder(int candidateId, int projectId, int externalClientId, int successProfileId, ProjectHubAssessments projectAssessments, IScoreExtract scoreExtract, ICommonTextLookup textLookup)
        {
            this.externalClientId = externalClientId;
            this.successProfileId = successProfileId;

            Check.NotNull(projectAssessments);
            this.projectAssessments = projectAssessments;

            Check.NotNull(textLookup);
            this.textLookup = textLookup;

            scores = new Lazy<ScoreResult>(() => GetScores(candidateId, projectId, scoreExtract));
        }

        /// <summary>
        /// Gets calculator specific scores
        /// </summary>
        /// <returns>A list of calculator scores</returns>
        public IEnumerable<CalculatorScore> BuildCalculatorScores()
        {
            var overallScore = scores.Value?.GetScore(ScoreGroup.Overall, ScoreKey.OverallFit);
            if (overallScore != null)
            {
                return new List<CalculatorScore>
                {
                    new CalculatorScore(CalculatorScoreGroup.HubFit, CalculatorScoreType.Fit, overallScore.Fit.Value)
                };
            }

            return Enumerable.Empty<CalculatorScore>();
        }

        /// <summary>
        /// Builds the results per assessment
        /// </summary>
        /// <returns>A list of assessment results</returns>
        public IEnumerable<OrderPackageAssessmentResult> BuildAssessmentResults()
            => new List<OrderPackageAssessmentResult>()
                .AddIfNotNull(BuildCompetencies())
                .AddIfNotNull(BuildTraits())
                .AddIfNotNull(BuildDrivers())
                .AddIfNotNull(BuildAbility());

        /// <summary>
        /// Builds an ability result.
        /// Overall result is the fit score (count of assessment fit scores that reach the target score).
        /// Detailed results are one per measure (numerical, verbal etc.).
        /// </summary>
        /// <returns>Null if ability not measured, otherwise a <see cref="OrderPackageAssessmentResult"/></returns>
        private OrderPackageAssessmentResult BuildAbility()
        {
            var abilityAssessments = projectAssessments.AllToMeasure.Where(a => a.BlendedType.IsAbility());
            if (!abilityAssessments.Any())
            {
                log.Trace("No ability assessments measured, skipping");
                return null;
            }

            var overallAbility = scores.Value?.GetScore(ScoreGroup.Overall, ScoreKey.Ability);
            if (overallAbility is null)
            {
                log.Warn("Ability overall score not found, skipping");
                return null;
            }

            var result = new OrderPackageAssessmentResult
            {
                AssessmentOverall = overallAbility.ToResult(textLookup.Ability)
            };

            foreach (var abilityAssessment in abilityAssessments)
            {
                var abilityScore = scores.Value.GetScore(ScoreGroup.Ability, AbilityBlendedTypeScoreKey[abilityAssessment.BlendedType]);
                var assessmentResult = abilityScore.ToResult(textLookup.GetBlendedAssessmentName(abilityAssessment.BlendedType));
                result.AddAssessmentResult(assessmentResult);
            }

            result.AssessmentOverall.Summary = ResultsHelper.CreateResultsSummary(result.AssessmentDetails.Select(a => a.AssessmentDetail), r => r.FitScore);
            return result;
        }

        /// <summary>
        /// Builds a competencies assessment result.
        /// Overall result is the fit score (count of reported competencies that reach the target score).
        /// Detailed results are one per competency.
        /// </summary>
        /// <returns>Null if competencies not measured, otherwise a <see cref="OrderPackageAssessmentResult"/></returns>
        private OrderPackageAssessmentResult BuildCompetencies() =>
            BuildCompetenciesOrTraits(
                BlendedAssessmentType.Competencies,
                ScoreKey.Competencies,
                ScoreGroup.Competency,
                projectAssessments.Behavioural?.Report,
                code => textLookup.GetCompetencyName(externalClientId, successProfileId, code));

        /// <summary>
        /// Builds a traits assessment result.
        /// Overall result is the fit score (count of reported traits that reach the target score).
        /// Detailed results are one per trait.
        /// </summary>
        /// <returns>Null if traits not measured, otherwise a <see cref="OrderPackageAssessmentResult"/></returns>
        private OrderPackageAssessmentResult BuildTraits() =>
            BuildCompetenciesOrTraits(
                BlendedAssessmentType.Traits,
                ScoreKey.Traits,
                ScoreGroup.Traits,
                projectAssessments.Traits?.Report,
                code => textLookup.GetTraitName(code));

        private OrderPackageAssessmentResult BuildCompetenciesOrTraits(
            BlendedAssessmentType blendedAssessmentType,
            string scoreKey,
            ScoreGroup scoreGroup,
            IEnumerable<string> codes,
            Func<string, string> codeNameLookup)
        {
            var overall = CheckMeasuredAndGetOverallScore(blendedAssessmentType, scoreKey);
            if (overall is null)
            {
                return null;
            }

            var result = new OrderPackageAssessmentResult
            {
                AssessmentOverall = overall.ToResult(textLookup.GetBlendedAssessmentName(blendedAssessmentType))
            };

            foreach (var code in codes)
            {
                var score = scores.Value.GetScore(scoreGroup, code);
                if (score is null)
                {
                    log.Warn($"Could not find score for code {code}, skipping");
                    continue;
                }

                var codeResult = score.ToResult(codeNameLookup(code));
                result.AddAssessmentResult(codeResult);
            }

            result.AssessmentOverall.Summary = ResultsHelper.CreateResultsSummary(result.AssessmentDetails.Select(s => s.AssessmentDetail), r => r.FitScore);
            return result;
        }

        /// <summary>
        /// Builds a drivers assessment result.
        /// OVerall result is the fit score (count of drivers that reach the target score).
        /// Detailed results are one per driver.
        /// </summary>
        /// <returns>Null if drivers not measured, otherwise a <see cref="OrderPackageAssessmentResult"/> </returns>
        private OrderPackageAssessmentResult BuildDrivers()
        {
            var overallDrivers = CheckMeasuredAndGetOverallScore(BlendedAssessmentType.Drivers, ScoreKey.Drivers);
            if (overallDrivers is null)
            {
                return null;
            }

            var result = new OrderPackageAssessmentResult
            {
                AssessmentOverall = overallDrivers.ToResult(textLookup.GetBlendedAssessmentName(BlendedAssessmentType.Drivers))
            };

            foreach (var driversScore in scores.Value.ScoreItems.Where(s => s.Key.Group == ScoreGroup.Drivers))
            {
                var driversResult = driversScore.Value.ToResult(textLookup.GetDriverName(driversScore.Key.Key));
                result.AddAssessmentResult(driversResult);
            }

            result.AssessmentOverall.Summary = ResultsHelper.CreateResultsSummary(result.AssessmentDetails.Select(a => a.AssessmentDetail), r => r.FitScore);
            return result;
        }

        private KfasScoreItem CheckMeasuredAndGetOverallScore(BlendedAssessmentType blendedAssessmentType, string scoreKey)
        {
            if (!projectAssessments.IsMeasured(blendedAssessmentType))
            {
                log.Trace($"{blendedAssessmentType} not measured, skipping");
                return null;
            }

            var overallScoreItem = scores.Value?.GetScore(ScoreGroup.Overall, scoreKey);
            if (overallScoreItem is null)
            {
                log.Warn($"{scoreKey} overall score not found, skipping");
                return null;
            }

            return overallScoreItem;
        }

        /// <summary>
        /// Gets all fit scores for the candidate/project
        /// </summary>
        /// <param name="candidateId">Id of the candidate</param>
        /// <param name="projectId">Id of the project</param>
        /// <param name="scoreExtract">The score extract service to use</param>
        /// <returns>A <see cref="ScoreResult"/> if scores exist, otherwise null</returns>
        private ScoreResult GetScores(int candidateId, int projectId, IScoreExtract scoreExtract)
        {
            var projectScores = scoreExtract.GetScores(new ExtractScoresRequest
            {
                CandidateIds = new List<int> { candidateId },
                ProjectIds = new List<int> { projectId },
                ScoreTypes = new List<ScoreType> { ScoreType.OverallFit }
            })?.Candidates
            .FirstOrDefault()?
            .Projects
            .FirstOrDefault()?
            .Scores;

            if (projectScores is null || !projectScores.Any())
            {
                log.Warn($"Cannot find scores for candidateId {candidateId}, projectId {projectId}, scoreType {ScoreType.OverallFit}");
                return null;
            }

            return projectScores.First().Value;
        }
    }
}
