﻿using System;
using System.Collections.Generic;
using System.Linq;
using KornFerry.Services.Content.Sjt;
using KornFerry.Services.Content.TextLookup.Sjt;
using KornFerry.Services.Extracts.Service;
using KornFerry.Services.Scoring.Shared.Storage;
using KornFerry.Services.Scoring.Shared.Storage.Models;
using NLog;
using TalentQ.Integrations.KFATS.Common.Models;
using TalentQ.Integrations.KFATS.Common.Results;
using TalentQ.Integrations.KFATS.Hub.Results.Configuration;
using TalentQ.Utilities;
using TQLogic.Assessments.Player;
using TQLogic.Projects;
using TQLogic.Projects.Hub;

namespace TalentQ.Integrations.KFATS.Hub.Results
{
    /// <summary>
    /// Score builder for SJTs within a package
    /// </summary>
    public class SjtScoreBuilder : IScoreBuilder
    {
        private static readonly Logger Log = LogManager.GetCurrentClassLogger();
        private readonly Lazy<ScoreResult> scores;
        private readonly Lazy<IEnumerable<SjtScale>> scales;
        private readonly ProjectHub project;
        private readonly AssessmentResultBuilderConfiguration configuration;
        private readonly ISjtTextLookup sjtTextLookup;

        /// <summary>
        /// A new <see cref="SjtScoreBuilder"/>
        /// </summary>
        /// <param name="candidateId">Id of the candidate to get scores for</param>
        /// <param name="project">The project hub to get scores for</param>
        /// <param name="configuration">Result configuration</param>
        /// <param name="sjtTextLookup">An sjt text lookup service</param>
        /// <param name="scoreExtract">A score extract service</param>
        public SjtScoreBuilder(int candidateId, ProjectHub project, AssessmentResultBuilderConfiguration configuration, IScoreExtract scoreExtract, ISjtTextLookup sjtTextLookup)
        {
            Check.NotNull(project);
            Check.NotNull(sjtTextLookup);
            this.project = project;
            this.configuration = configuration;
            this.sjtTextLookup = sjtTextLookup;
            scoreExtract = scoreExtract ?? new ScoreDbExtract();
            scores = new Lazy<ScoreResult>(() => GetScores(candidateId, scoreExtract));
            scales = new Lazy<IEnumerable<SjtScale>>(GetSjtScales);
        }

        /// <summary>
        /// Gets calculator specific scores
        /// </summary>
        /// <returns>A list of calculator scores</returns>
        public IEnumerable<CalculatorScore> BuildCalculatorScores()
        {
            var overallScore = scores.Value?.GetScore(ScoreGroup.Overall, OverallScaleId.ToString());
            if (overallScore is null)
            {
                Log.Info($"Could not find overall SJT score (scale id {OverallScaleId})");
                return Enumerable.Empty<CalculatorScore>();
            }

            return new List<CalculatorScore>
            {
                new CalculatorScore(CalculatorScoreGroup.SJT, CalculatorScoreType.Fit, overallScore.Fit.Value),
                new CalculatorScore(CalculatorScoreGroup.SJT, CalculatorScoreType.Percentile, overallScore.Percentile.Value)
            };
        }

        /// <summary>
        /// Builds the results per assessment
        /// </summary>
        /// <returns>A list of assessment results</returns>
        public IEnumerable<OrderPackageAssessmentResult> BuildAssessmentResults()
        {
            var overallScore = scores.Value?.GetScore(ScoreGroup.Overall, OverallScaleId.ToString());
            if (overallScore is null)
            {
                Log.Info($"Could not find overall SJT score (scale id {OverallScaleId})");
                return Enumerable.Empty<OrderPackageAssessmentResult>();
            }

            var result = new OrderPackageAssessmentResult
            {
                AssessmentOverall = overallScore.ToResult(sjtTextLookup.GetName())
            };

            foreach (var scale in scales.Value.Where(s => s.Type == PlayerScaleType.Competency))
            {
                var score = scores.Value.GetScore(ScoreGroup.Competency, scale.Id.ToString());
                if (score is null)
                {
                    Log.Warn($"Could not find score for scale {scale.Id}, skipping");
                    continue;
                }

                var scaleResult = score.ToResult(scale.Name);
                result.AddAssessmentResult(scaleResult);
            }

            var summaryScore = configuration?.SummaryScore ?? project.ScoreDisplay;
            object summaryScoreSelector(Result r) => summaryScore == ScoreDisplayType.Percentile ? r.Percentile : r.FitScore;
            result.AssessmentOverall.Summary = ResultsHelper.CreateResultsSummary(result.AssessmentDetails.Select(a => a.AssessmentDetail), summaryScoreSelector);
            return new List<OrderPackageAssessmentResult> { result };
        }

        private int OverallScaleId => scales.Value.First(s => s.Type == PlayerScaleType.Assessment).Id;

        private IEnumerable<SjtScale> GetSjtScales()
        {
            var scales = sjtTextLookup.GetScales(1);
            if (scales == null || !scales.Any())
            {
                throw new Exception($"Could not find any scales");
            }

            return scales.Where(s => s.Display);
        }

        /// <summary>
        /// Gets the candidate's SJT scores
        /// </summary>
        /// <param name="candidateId">CandidateId of the candidate to get scores for</param>
        /// <param name="scoreExtract">The score extract service to use</param>
        /// <returns>SJT scores if found, otherwise null</returns>
        private ScoreResult GetScores(int candidateId, IScoreExtract scoreExtract)
        {
            var projectScores = scoreExtract.GetScores(new ExtractScoresRequest
            {
                CandidateIds = new List<int> { candidateId },
                ProjectIds = new List<int> { project.ProjectId.Value },
                ScoreTypes = new List<ScoreType> { ScoreType.SJT }
            })?.Candidates
            .FirstOrDefault()?
            .Projects
            .FirstOrDefault()?
            .Scores;

            if (projectScores is null || !projectScores.Any())
            {
                Log.Warn($"Cannot find scores for candidateId {candidateId}, projectId {project.ProjectId}, scoreType {ScoreType.SJT}");
                return null;
            }

            return projectScores.First().Value;
        }
    }
}
