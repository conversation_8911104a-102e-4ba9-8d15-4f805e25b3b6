﻿using KornFerry.Services.Content;
using KornFerry.Services.Content.TextLookup.Sjt;
using KornFerry.Services.Extracts.Service;
using KornFerry.Services.Reporting.Blended.Services;
using KornFerry.Services.Reporting.Blended.Services.General;
using KornFerry.Services.Service.ProductsApi;
using System.Collections.Generic;
using TalentQ.Integrations.Hub;
using TalentQ.Integrations.KFATS.Common;
using TalentQ.Integrations.KFATS.Common.Models;
using TalentQ.Integrations.KFATS.Common.Results;
using TalentQ.Utilities;
using TQLogic;
using TQLogic.Assessments.Player;
using TQLogic.Projects;

namespace TalentQ.Integrations.KFATS.Hub.Results
{
    /// <summary>
    /// Build the results of a single package
    /// </summary>
    public class PackageResultBuilder
    {
        private readonly IScoreExtract scoreExtract;
        private readonly IHubRepository hubRepository;
        private readonly ICommonTextLookup textLookup;
        private ISjtTextLookup sjtTextLookup;
        private readonly IReportNameService reportDisplayNameService;
        private readonly IReportRepository reportRepository;
        private readonly IProductsApiService productsApiService;
        private readonly IPlayerRepository playerRepository;
        private readonly KfatsHandler handler;
        private readonly Package package;
        private readonly ProductsApiRegion region;
        private readonly IOrderRepository orderRepository;

        /// <summary>
        /// The language to use for translations of text within results, as taken from <see cref="package"/> config
        /// </summary>
        private Language Language => LanguageCollection.Default.LoadedLanguages[package.ResultsConfiguration.LanguageId];

        /// <summary>
        /// A new <see cref="PackageResultBuilder"/>
        /// </summary>
        /// <param name="handler">The handler (must be for the hub)</param>
        /// <param name="package">The package to build the results for</param>
        /// <param name="region">The region to use for result URLs</param>
        /// <param name="scoreExtract">Optional score extract service</param>
        /// <param name="hubRepository">Optional hub repository</param>
        /// <param name="textLookup">Optional common text lookup</param>
        /// <param name="sjtTextLookup">Optional sjt text lookup</param>
        /// <param name="reportDisplayNameService">Optional report display name service</param>
        /// <param name="reportRepository">Optional report repository</param>
        /// <param name="productsApiService">Optional products api service</param>
        /// <param name="playerRepository">Optional player test repository</param>
        /// <param name="orderRepository">Optional Order Repository</param>
        public PackageResultBuilder(
            KfatsHandler handler,
            Package package,
            ProductsApiRegion region = ProductsApiRegion.US,
            IScoreExtract scoreExtract = null,
            IHubRepository hubRepository = null,
            ICommonTextLookup textLookup = null,
            ISjtTextLookup sjtTextLookup = null,
            IReportNameService reportDisplayNameService = null,
            IReportRepository reportRepository = null,
            IProductsApiService productsApiService = null,
            IPlayerRepository playerRepository = null,
            IOrderRepository orderRepository = null
            )
        {
            Check.IsTrue(handler.IsHub());
            Check.NotNull(package);
            Check.NotNull(package.ResultsConfiguration);
            this.handler = handler;
            this.package = package;
            this.region = region;
            this.scoreExtract = scoreExtract ?? new ScoreDbExtract();
            this.hubRepository = hubRepository ?? new HubDbRepository();
            this.textLookup = textLookup ?? new CommonTextLookup(Language);
            this.sjtTextLookup = sjtTextLookup;
            this.reportDisplayNameService = reportDisplayNameService ?? new ReportNameCmsService();
            this.reportRepository = reportRepository ?? new ReportDbRepository();
            this.productsApiService = productsApiService ?? new ProductsApiServiceCached();
            this.playerRepository = playerRepository ?? new PlayerDbRepository();
            this.orderRepository = orderRepository ?? new OrderDbRepository();
        }

        /// <summary>
        /// Build a package result for a specific candidate
        /// </summary>
        /// <param name="candidateUser">The candidate to build results for</param>
        /// <param name="packageStatus">The status of the candidate's package instance</param>
        /// <param name="kfOrderId">The order id</param>
        /// <returns>An <see cref="OrderPackageResult"/></returns>
        public OrderPackageResult Build(TQUser candidateUser, PackageStatus packageStatus, string kfOrderId)
        {
            var result = new OrderPackageResult
            {
                KFATSPackageId = packageStatus.KfAtsPackageId,
                ProviderPackageId = packageStatus.PackageId
            };

            var project = hubRepository.GetProjectHub(package.ProjectId);
            var scoreBuilder = GetScoreBuilder(project, candidateUser.Candidate.CandidateID);
            if (scoreBuilder != null)
            {
                SetAssessmentResults(result, scoreBuilder);
                SetOverallResult(result, packageStatus.Status, scoreBuilder);
            }

            SetUrls(result, packageStatus.Status, candidateUser, project, kfOrderId);
            return result;
        }

        /// <summary>
        /// Sets assessment results. One <see cref="OrderPackageAssessmentResult"/> per type of assessment (e.g. competencies) containing overall and detailed/competency results.
        /// </summary>
        /// <param name="result">The <see cref="OrderPackageResult"/> to add to</param>
        /// <param name="scoreBuilder">The score builder to get scores</param>
        private void SetAssessmentResults(OrderPackageResult result, IScoreBuilder scoreBuilder) => result.Assessments.AddRange(scoreBuilder.BuildAssessmentResults());

        /// <summary>
        /// Sets the <see cref="OverallResult"/> of the package
        /// </summary>
        /// <param name="result">The package result to set the overall result of</param>
        /// <param name="packageStatus">The status of the package</param>
        /// <param name="scoreBuilder">The score bulder to get scores</param>
        private void SetOverallResult(OrderPackageResult result, IntegrationStatus packageStatus, IScoreBuilder scoreBuilder)
        {
            var config = package.ResultsConfiguration.OverallResult ?? new OverallResultConfiguration();
            IEnumerable<CalculatorScore> calculatorScoresFunc() => scoreBuilder.BuildCalculatorScores();
            PackageOverallResultBuilder.SetOverallResult(result, packageStatus, config, calculatorScoresFunc);
        }

        /// <summary>
        /// Sets the urls within the package result
        /// </summary>
        /// <param name="result">The result to set the urls of</param>
        /// <param name="packageStatus">The status of the package</param>
        /// <param name="candidateUser">The <see cref="TQUser"/> of the candidate these results are for</param>
        /// <param name="project">The related project</param>
        /// <param name="kfOrderId">The order id for this package/candidate combination</param>
        private void SetUrls(OrderPackageResult result, IntegrationStatus packageStatus, TQUser candidateUser, ProjectHub project, string kfOrderId)
        {
            var urlBuilder = new PackageURLResultBuilder(handler, productsApiService, hubRepository, orderRepository);  

            // Web aka More details page aka Participant details page
            if (package.ResultsConfiguration.IncludeIntegrationDetailsUrl)
            {
                result.Urls.Add(urlBuilder.BuildWebUrl(project.ProjectId.Value, candidateUser.Candidate.CandidateID, region));
            }           

            // Pdf urls
            if (packageStatus == IntegrationStatus.Completed && package.ResultsConfiguration.IncludePdfUrls)
            {
                var clientReportService = new ClientReportService(project.Project.ClientOwnerId, textLookup.Language.ProductsLocale, reportDisplayNameService, hubRepository, reportRepository);
                result.Urls.AddRange(urlBuilder.BuildPdfUrls(project, candidateUser, kfOrderId, package.PackageId, textLookup, clientReportService, package.ResultsConfiguration.PdfUrlsReportFilter, package.ResultsConfiguration.PdfUrlsUseParticipantLanguage));
            }
        }

        /// <summary>
        /// Creates a score builder based on project configuration
        /// </summary>
        /// <param name="project">The project to create a scores builder based on</param>
        /// <param name="candidateId">CandidateId of the candidate these results are for</param>
        /// <returns>A <see cref="IScoreBuilder"/> if scores are supported, otherwise null</returns>
        private IScoreBuilder GetScoreBuilder(ProjectHub project, int candidateId)
        {
            if (project.Assessments.IsMeasured(TQLogic.Assessments.BlendedAssessmentType.SJT))
            {
                if (sjtTextLookup is null)
                {
                    var playerTestId = project.Assessments.SJT.AssessmentId;
                    sjtTextLookup = new SjtTextLookupFactory().Get(playerTestId, Language, playerRepository);
                }

                return new SjtScoreBuilder(candidateId, project, package.ResultsConfiguration.AssessmentResults, scoreExtract, sjtTextLookup);
            }

            if (project.RequiresBlendedScores)
            {
                return new FitScoreBuilder(candidateId, project.ProjectId.Value, project.ExternalClientId.Value, project.SuccessProfileId.Value, project.Assessments, scoreExtract, textLookup);
            }

            return null;
        }
    }
}
