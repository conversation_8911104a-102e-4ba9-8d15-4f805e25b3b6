﻿using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using TalentQ.Integrations.Hub.Authorization;
using TalentQ.Integrations.Hub.Models;
using TQCommon;
using TQLogic.Assessments;

namespace TalentQ.Integrations.Hub.KFOne.Project.Models
{
    /// <summary>
    /// Details to create a project, minimal details provided by the KF One system.
    /// </summary>
    public class KFOneProjectCreateUpdate : ClientModel, IAuthProjects
    {
        /// <summary>
        /// Id of the project. Null if new.
        /// </summary>
        /// <remarks>Serialization order by default is -1</remarks>
        [JsonProperty("projectId", Order = -4)]
        public int? ProjectId { get; set; }

        /// <summary>
        /// Name of the project
        /// </summary>
        [JsonProperty("name", Order = -3)]
        public string Name { get; set; }

        /// <summary>
        /// The name of the platform for the project, this is required for the KF One system. 
        /// </summary>
        [JsonProperty("platform")]
        public string Platform { get; set; }

        /// <summary>
        /// The norm country location code e.g. GBR
        /// </summary>
        [JsonProperty("location")]
        public string Location { get; set; }

        /// <summary>
        /// Optional end date time of the project, in UNIX epoch format, milliseconds.
        /// </summary>
        [JsonProperty("endDateTime")]
        public long? EndDateTime { get; set; }

        /// <summary>
        /// Id of the external success profile
        /// </summary>
        [JsonProperty("successProfileId")]
        public int? SuccessProfileId { get; set; }

        /// <summary>
        /// The norm name, either Global or the regional norm country code.
        /// </summary>
        [JsonProperty("norm")]
        public string Norm { get; set; }

        /// <summary>
        /// Optional potential levels for the project, can be used instead of success profile.
        /// </summary>
        [JsonProperty("potentialLevels")]
        public PotentialLevels PotentialLevels { get; set; }

        /// <summary>
        /// The list of assessments to included in the project.
        /// </summary>
        [JsonProperty("assessments")]
        public ProjectAssessments Assessments { get; set; }

        /// <summary>
        /// Defined as dfrReports in the KF One system, but actually the participants reports in KFAS.
        /// The participant reports must define DFR reports for a participant to be able to generate DFR reports. 
        /// Without this property DFR reports will not be generated.
        /// </summary>
        [JsonProperty("dfrReports")]
        public IEnumerable<string> DfrReports { get; set; }

        /// <summary>
        /// For <see cref="IAuthProjects"/>
        /// </summary>
        [JsonIgnore]
        public IEnumerable<int> ProjectIds => ProjectId.HasValue ? new List<int> { ProjectId.Value } : Enumerable.Empty<int>();
    }

    /// <summary>
    /// The configuration for each of the assessments to be included in the project.
    /// </summary>
    public class ProjectAssessments
    {
        /// <summary>
        /// All assessment properties within this instance
        /// </summary>
        [JsonIgnore]
        public IEnumerable<MeasuredProjectAssessment> All
        {
            get
            {
                yield return AbstractReasoning;
                yield return Checking;
                yield return Competencies;
                yield return Drivers;
                yield return Experiences;
                yield return InclusiveLeaderSJT;
                yield return Logical;
                yield return Numerical;
                yield return Preferences;
                yield return TechnicalSkillsInventory;
                yield return Traits;
                yield return Verbal;
            }
        }

        /// <summary>
        /// The list of assessments which have the measured flag set.
        /// </summary>
        [JsonIgnore]
        public IEnumerable<MeasuredProjectAssessment> Measured => All.Where(m => m?.Measure ?? false);

        /// <summary>
        /// The Abstract Reasoning (also known as Ravens or Capacity assessment) ability assessment
        /// </summary>
        public MeasuredProjectAssessment AbstractReasoning { get; set; }

        /// <summary>
        /// The Aspect checking ability assessment
        /// </summary>
        public MeasuredProjectAssessment Checking { get; set; }

        /// <summary>
        /// The competencies typed assessments, e.g. KF4D or Entry level competencies assessment
        /// </summary>
        public TypedProjectAssessment Competencies { get; set; }

        /// <summary>
        /// KF4D Drivers assessment
        /// </summary>
        public MeasuredProjectAssessment Drivers { get; set; }

        /// <summary>
        /// Experiences assessment
        /// </summary>
        public MeasuredProjectAssessment Experiences { get; set; }

        /// <summary>
        /// The inclusive leader SJT
        /// </summary>
        public MeasuredProjectAssessment InclusiveLeaderSJT { get; set; }

        /// <summary>
        /// The Elements Logical ability assessment
        /// </summary>
        public MeasuredProjectAssessment Logical { get; set; }

        /// <summary>
        /// The numerical ability assessment, either Aspects Numerical or Elements Numerical
        /// </summary>
        public TypedProjectAssessment Numerical { get; set; }

        /// <summary>
        /// KF4D Traits assessment
        /// </summary>
        public MeasuredProjectAssessment Traits { get; set; }

        /// <summary>
        /// The Verbal ability assessment, either Aspects Verbal or Elements Verbal
        /// </summary>
        public TypedProjectAssessment Verbal { get; set; }

        /// <summary>
        /// Preferences assessment, required if Potential levels are defined in the project.
        /// </summary>
        public MeasuredProjectAssessment Preferences { get; set; }

        /// <summary>
        /// Technical skill inventory assessment, a success profile is required for this assessment.
        /// </summary>
        public MeasuredProjectAssessment TechnicalSkillsInventory { get; set; }
    }

    /// <summary>
    /// Basic measured property for assessments
    /// </summary>
    public class MeasuredProjectAssessment
    {
        /// <summary>
        /// Determine if the assessment is to be included in the project
        /// </summary>
        public bool Measure { get; set; }
    }

    /// <summary>
    /// Assessment which requires the actual type to be provided
    /// </summary>
    public class TypedProjectAssessment : MeasuredProjectAssessment
    {
        /// <summary>
        /// Will either be AssessmentType or a SubTestId, depending on Type
        /// </summary>
        [JsonProperty("assessmentType")]
        public string AssessmentType { get; set; }

        /// <summary>
        /// The KFAS assessment type based on <see cref="AssessmentType"/>
        /// </summary>
        [JsonIgnore]
        public virtual AssessmentType Type => Enum<AssessmentType>.ConvertTo(AssessmentType);
    }

    /// <summary>
    /// Optional potential levels for the project, can be used instead of success profile.
    /// </summary>
    public class PotentialLevels
    {
        /// <summary>
        /// The current potential level for the project.
        /// </summary>
        [JsonProperty("currentLevel")]
        public string CurrentLevel { get; set; }

        /// <summary>
        /// The target potential level for the project.
        /// </summary>
        [JsonProperty("targetLevel")]
        public string TargetLevel { get; set; }
    }
}
