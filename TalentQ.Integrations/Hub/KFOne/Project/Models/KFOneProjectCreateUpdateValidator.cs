﻿using FluentValidation;
using KornFerry.Services.Scoring.Shared;
using KornFerry.Services.Service.ProductsApi;
using System;
using System.Linq;
using System.Threading.Tasks;
using TalentQ.Integrations.Hub.Validators;
using TQCommon;
using TQLogic.Assessments;
using TQLogic.Validation.Complex;

namespace TalentQ.Integrations.Hub.KFOne.Project.Models
{
    /// <summary>
    /// Validation for a KF One <see cref="KFOneProjectCreateUpdate"/> model
    /// </summary>
    public class KFOneProjectCreateUpdateValidator : AbstractValidator<KFOneProjectCreateUpdate>
    {
        private readonly IProductsApiService productsApiService;
        private readonly IClientReportService clientReportService;

        /// <summary>
        /// Initializes a new instance of the <see cref="KFOneProjectCreateUpdateValidator"/> class.
        /// </summary>
        /// <param name="productsApiService">Optional products api service to use for validating against external data</param>
        /// <param name="clientReportService">Client report service to check the reports available for a client</param>
        public KFOneProjectCreateUpdateValidator(IProductsApiService productsApiService = null, IClientReportService clientReportService = null)
        {
            this.productsApiService = productsApiService ?? new ProductsApiServiceCached();
            this.clientReportService = clientReportService ?? throw new ArgumentNullException(nameof(clientReportService));

            GeneralRules();
            SuccessProfileRules();
            PotentialRules();
            AssessmentRules();
            ReportsAvailableToParticipantRules();
        }

        private void GeneralRules()
        {
            // Client
            RuleFor(m => m.ExternalClientId)
                .GreaterThan(0).WithMessage("Must provide a client id to associate this project to");

            // Platform - No need to validate the value of the platform, simply one has been provided.
            RuleFor(m => m.Platform)
                .NotEmpty().WithMessage("Must provide a platform")
                .MaximumLength(25).WithMessage("Platform must be less than {MaxLength}, but found {TotalLength}");

            // Project name
            RuleFor(m => m.Name)
                .NotEmpty().WithMessage("Must provide a project name")
                .MaximumLength(ProjectNameRule.MaxLength).WithMessage("Project name must be less than {MaxLength}, but found {TotalLength}")
                .Matches(ProjectNameRule.RegexPattern).WithMessage("Project name must not contain any of the following characters <>\"%;&#");

            // Location
            RuleFor(m => m.Location)
                .Cascade(CascadeMode.StopOnFirstFailure)
                .NotEmpty().WithMessage("Must provide a location")
                .MustAsync(async (m, p, _) => (await productsApiService.GetRegionalNormsAsync((int)m.ExternalClientId))?.Data?.Locations?.Any(l => l.CountryCode.Equals(p, StringComparison.OrdinalIgnoreCase)) ?? false)
                .WithMessage(m => $"Invalid location {m.Location} unable to find in API data");
        }

        private void SuccessProfileRules()
        {
            When(m => m.SuccessProfileId.HasValue, () => 
            {
                // Success profile id
                RuleFor(m => m.SuccessProfileId)
                    .GreaterThan(0).WithMessage("Must provide a success profile id greater than 0");

                // Norm
                RuleFor(m => m.Norm)
                    .Cascade(CascadeMode.StopOnFirstFailure)
                    .NotEmpty().WithMessage("Must provided a norm")
                    .MustAsync(async (m, p, _) => (await productsApiService.GetRegionalNormsAsync((int)m.ExternalClientId))?.Data?.RegionalNorms?.Any(n => n.Country.Equals(p, StringComparison.OrdinalIgnoreCase)) ?? false)
                    .WithMessage(m => $"Invalid norm {m.Norm} unable to find value in API data");
            });
        }

        private void PotentialRules()
        {
            // Potential
            RuleFor(m => m.PotentialLevels).SetValidator(new PotentialLevelsValidator());

            // Preferences, Traits and Drivers assessments are required if using Potential levels,
            // they are always enabled automatically when created via the Talent Hub UI.
            When(m => m.PotentialLevels != null && m.Assessments != null, () =>
            {
                RuleFor(m => m.Assessments.Preferences)
                    .Cascade(CascadeMode.StopOnFirstFailure)
                    .NotNull().WithMessage("Must provide the Preferences assessment when using Potential levels")
                    .Must(m => m.Measure).WithMessage("Must enabled the Preferences assessment when using Potential levels");

                RuleFor(m => m.Assessments.Traits)
                   .Cascade(CascadeMode.StopOnFirstFailure)
                   .NotNull().WithMessage("Must provide the Traits assessment when using Potential levels")
                   .Must(m => m.Measure).WithMessage("Must enabled the Traits assessment when using Potential levels");

                RuleFor(m => m.Assessments.Drivers)
                   .Cascade(CascadeMode.StopOnFirstFailure)
                   .NotNull().WithMessage("Must provide the Drivers assessment when using Potential levels")
                   .Must(m => m.Measure).WithMessage("Must enabled the Drivers assessment when using Potential levels");
            });
        }

        private void AssessmentRules()
        {
            // Assessment contents
            RuleFor(m => m.Assessments)
                .NotNull().WithMessage("Must provide the assessments configuration")
                .SetValidator(m => new ProjectAssessmentsValidator());

            // Technical skills inventory assessment uses the skills from the success profile.
            // If not using a success profile, then the assessment cannot be used.
            When(m => m.Assessments?.TechnicalSkillsInventory?.Measure == true, () =>
            {  
                RuleFor(m => m.SuccessProfileId)
                    .NotNull().WithMessage("Must provide a success profile id when measuring Technical Skill Inventory assessment")
                    .GreaterThan(0).WithMessage("Must provide a success profile id greater than 0 when measuring Technical Skill Inventory assessment");
            });
        }

        private void ReportsAvailableToParticipantRules()
        {
            When(m => m.DfrReports?.Any() ?? false, () =>
            {
                RuleFor(m => m.DfrReports)
                    .Must(n => clientReportService.CheckforValidParticipantReport(null, n)).WithMessage("Cannot find one of the ReportsAvailableToParticipant for client");
            });
        }

        /// <summary>
        /// Validate the model.
        /// </summary>
        /// <param name="model">The model to validate</param>
        /// <param name="externalClientId">The external client id the project is to be associated with</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task ValidateAsync(KFOneProjectCreateUpdate model, int externalClientId)
        {
            var result = await ValidateAsync(model);
            if (!result.IsValid)
            {
                throw new HubException(HubErrorCode.InvalidRequestDetails, result.Errors.Select(e => e.ErrorMessage).ToCommaString());
            }
        }
    }

    /// <summary>
    /// Validator for the <see cref="PotentialLevels"/> class.
    /// </summary>
    public class PotentialLevelsValidator : AbstractValidator<PotentialLevels>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="PotentialLevelsValidator" /> class.
        /// </summary>
        public PotentialLevelsValidator()
        {
            // Current level
            RuleFor(m => m.CurrentLevel)
                .Cascade(CascadeMode.StopOnFirstFailure)
                .NotEmpty().WithMessage("Potential current level is expected")
                .Must(l => PotentialLevelMapping.IsValidLevel(l)).WithMessage("Potential current Level {PropertyValue} is not valid");

            // Target level
            RuleFor(m => m.TargetLevel)
                .Cascade(CascadeMode.StopOnFirstFailure)
                .NotEmpty().WithMessage("Potential target level is expected")
                .Must(l => PotentialLevelMapping.IsValidLevel(l)).WithMessage("Potential target Level {PropertyValue} is not valid")
                .Must((m, targetLevel) => PotentialLevelMapping.Position(targetLevel) <= PotentialLevelMapping.Position(m.CurrentLevel))
                    .WithMessage("Potential target level must be a lower level than current level");
        }
    }

    /// <summary>
    /// Validator for the base <see cref="ProjectAssessments"/> class.
    /// </summary>
    /// <seealso cref="ProjectAssessments" />
    public class ProjectAssessmentsValidator : AbstractValidator<ProjectAssessments>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ProjectHubAssessmentValidator" /> class.
        /// </summary>
        public ProjectAssessmentsValidator()
        {
            RuleFor(m => m.Measured)
                .NotEmpty().WithMessage("At least one assessment must be measured");

            RuleFor(a => a.Competencies).SetValidator(new TypedProjectAssessmentValidator(nameof(ProjectAssessments.Competencies), AssessmentType.Kf4dBehaviours, AssessmentType.EntryLevelCompetency));
            RuleFor(a => a.Numerical).SetValidator(new TypedProjectAssessmentValidator(nameof(ProjectAssessments.Numerical), AssessmentType.AspectsNumerical, AssessmentType.ElementsNumerical));
            RuleFor(a => a.Verbal).SetValidator(new TypedProjectAssessmentValidator(nameof(ProjectAssessments.Verbal), AssessmentType.AspectsVerbal, AssessmentType.ElementsVerbal));
        }
    }

    /// <summary>
    /// Validator for the base <see cref="TypedProjectAssessment"/> class.
    /// </summary>
    /// <seealso cref="TypedProjectAssessment" />
    public class TypedProjectAssessmentValidator : AbstractValidator<TypedProjectAssessment>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="TypedProjectAssessmentValidator" /> class.
        /// </summary>
        /// <param name="fieldName">The name of the field that is being validated to include in the exception details.</param>
        /// <param name="allowedAssessmentTypes">The allowed assessment types.</param>
        public TypedProjectAssessmentValidator(string fieldName, params AssessmentType[] allowedAssessmentTypes)
        {
            When(a => a.Measure, () =>
            {
                RuleFor(a => a.AssessmentType)
                    .Cascade(CascadeMode.StopOnFirstFailure)
                    .NotEmpty().WithName($"{fieldName}: AssessmentType")
                    .Must((m, a) => allowedAssessmentTypes.Contains(m.Type)).WithMessage($"{fieldName}: AssessmentType must be one of {allowedAssessmentTypes.ToCommaString()}");
            });
        }
    }
}
