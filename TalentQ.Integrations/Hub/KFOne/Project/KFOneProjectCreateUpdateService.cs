﻿using KornFerry.Services.Scoring.Assessments;
using KornFerry.Services.Scoring.Project.Mapping;
using KornFerry.Services.Service.ProductsApi.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TalentQ.Integrations.Hub.KFOne.Project.Models;
using TalentQ.Integrations.Hub.Misc;
using TalentQ.Integrations.Hub.Models;
using TalentQ.Integrations.Hub.Models.EmailSetting;
using TalentQ.Utilities;
using TQLogic;
using TQLogic.Assessments;
using TQLogic.Jobs.Client;
using TQLogic.Proctoring;
using TQLogic.Projects;
using TQLogic.Projects.Hub;
using static TQCommon.NLogExtensions;

namespace TalentQ.Integrations.Hub.KFOne.Project
{
    /// <summary>
    /// KFAS project service used by KF One to create and update projects.
    /// </summary>
    public class KFOneProjectCreateUpdateService : ServiceBase
    {
        private static readonly Logger Log = LogManager.GetCurrentClassLogger();
        private readonly ProjectCreateUpdateService projectUpdateService;
        private readonly ProjectAssessmentService assessmentService;

        /// <summary>
        /// New <see cref="KFOneProjectCreateUpdateService"/>
        /// </summary>
        public KFOneProjectCreateUpdateService() : base()
        {
            projectUpdateService = new ProjectCreateUpdateService();
            assessmentService = new ProjectAssessmentService();
        }

        /// <summary>
        /// Create a KFAS project based on the minimal information provided by the KF One system.
        /// </summary>
        /// <param name="request">The data to create a project</param>
        /// <returns>Project details for the newly created project</returns>
        public async Task<KFOneProjectCreateUpdateResponse> CreateProjectAsync(KFOneProjectCreateUpdate request)
        {
            // Validate request
            var client = GetCreateRootClient(request.TQAdministratorUser, request.ExternalClientId);

            var clientReportService = new ClientReportServiceCached(client.ClientID, Language.ProductsLocale);
            var validator = new KFOneProjectCreateUpdateValidator(ProductsApiService, clientReportService);
            await validator.ValidateAsync(request, request.ExternalClientId).ConfigureAwait(false);

            // Create basic project details
            var norm = await GetNormAsync(request.Norm, request.ExternalClientId);
            var projectCreateUpdateRequest = new ProjectCreateUpdate
            {
                AllowAssessmentReuse = true,
                AssessmentsRaw = await GetAssessmentsAsync(request, norm),
                CandidatesAccessLearningContent = false,
                CommercialJson = GetCommercialJson(),
                CompletionNotifications = false,
                CurrentLevel = request.PotentialLevels?.CurrentLevel,
                CustomProjectTypeId = null,
                CustomProjectTypeName = null,
                EndDateTime = request.EndDateTime,
                ExternalClientId = request.ExternalClientId,
                InitiatedByJson = request.TQAdministratorUser.GetUAMUser(),
                IncludeDerailers = false,
                IncludeFitScore = false,
                IncludeLearningAgility = true,
                IsKFAssess2 = false,
                IsSuccessProfilePublished = true,
                InvitationSsoTemplateId = null,
                IsGlobalRole = false,
                IsNonBillable = false,
                Locale = request.Locale,
                LocationJson = await GetLocationJson(request),
                Name = request.Name,
                NormRaw = new JRaw(norm?.ToJson()),
                NotificationEmail = request.TQAdministratorUser.Person.EMail,
                OwnerJson = request.TQAdministratorUser.GetUAMUser(),
                Platform = request.Platform,
                ProjectScalesOnly = true,
                ProjectType = ProjectHubType.Generic.ToDescription(),
                ProjectDefaultNotificationTemplateId = null,
                ReminderNotificationTemplateId = null,
                ReportReleaseTemplateId = null,
                ReportsAvailableToParticipant = request.DfrReports,
                ScoreDisplay = ScoreDisplayType.Fit.ToDescription(),
                SelfService = false,
                SsoSetting = null,
                SuccessProfileCustomized = false,
                SuccessProfileId = request.SuccessProfileId,
                TargetLevel = request.PotentialLevels?.TargetLevel,
                TQAdministratorUser = request.TQAdministratorUser,
                Invisible = true
            };

            // Call the existing product hub save project
            var response = projectUpdateService.CreateProject(projectCreateUpdateRequest, projectCreateUpdateRequest.ProjectTypeEnum.ToHubProductType());

            Log.WithContext()
               .Property("ProjectId", response.ProjectId)
               .Property("SuccessProfileId", projectCreateUpdateRequest.SuccessProfileId)
               .Info(projectCreateUpdateRequest.SuccessProfileId.HasValue
                   ? $"Created project {response.ProjectId} with SuccessProfile ID: {projectCreateUpdateRequest.SuccessProfileId}"
                   : $"Created project {response.ProjectId} without a SuccessProfile ID");

            return new KFOneProjectCreateUpdateResponse
            {
                ProjectId = response.ProjectId
            };
        }

        /// <summary>
        /// Update the Success Profile of an existing KFAS project using the minimal information provided by the KF One system.
        /// </summary>
        /// <param name="request">The request model contains the necessary details for updating the existing project.</param>
        /// <returns>Basic project details</returns>
        public async Task<KFOneProjectCreateUpdateResponse> UpdateSuccessProfileAsync(KFOneSuccessProfileUpdate request)
        {
            var validator = new KFOneSuccessProfileUpdateValidator();
            await validator.ValidateAsync(request).ConfigureAwait(false);

            var projectHub = GetProjectHub(request.ProjectId.Value);
            if (projectHub.SuccessProfileId.HasValue)
            {
                throw new HubException(HubErrorCode.InvalidRequestDetails, $"The success profile for the project has already been established: {projectHub.SuccessProfileId}");
            }

            var successProfile = await ProductsApiService.GetSuccessProfileAsync(request.SuccessProfileId, request.ExternalClientId).ConfigureAwait(false);
            if (successProfile.Data == null)
            {
                throw new HubException(HubErrorCode.InvalidRequestDetails, $"Unable to get success profile, code: {successProfile.ResponseCode}, message: {successProfile.ResponseMessage}");
            }

            var normResult = await GetNormAsync(request.Norm, request.ExternalClientId);
            if (normResult == null)
            {
                throw new HubException(HubErrorCode.InvalidRequestDetails, $"Unable to find norm data for client: {request.ExternalClientId}");
            }        

            projectHub.NormJson = JsonConvert.SerializeObject(normResult);
            projectHub.LastModified = DateTime.Now;

            // If the success profile is published follow the same workflow as publishing a success profile
            if (successProfile != null && successProfile.Data != null && successProfile.Data.Status == SuccessProfileStatus.PUBLISHED)
            {
                HubRepository.SaveProject(projectHub);

                // Trigger job for reconfiguring project and rescore candidates  
                var data = new { ProjectId = projectHub.ProjectId.Value, successProfile.Data.Id, CandidateUserId = request.TQAdministratorUser.UserID };
                JobClient.Default.SendJob(request.TQAdministratorUser.UserID, "SuccessProfile/PublishProject", data);

                Log.WithContext()
                  .Property("ProjectId", request.ProjectId)
                  .Property("SuccessProfileId", request.SuccessProfileId)
                  .Info($"Project updated with success profile - {request.SuccessProfileId}");
            }
            else
            {
                projectHub.IsSuccessProfilePublished = false;
                projectHub.SuccessProfileId = request.SuccessProfileId;
                HubRepository.SaveProject(projectHub);
            }

            return new KFOneProjectCreateUpdateResponse
            {
                ProjectId = projectHub.ProjectId.Value
            };
        }

        private async Task<JRaw> GetAssessmentsAsync(KFOneProjectCreateUpdate request, ProjectHubNorm projectHubNorm)
        {
            ProjectHubAssessments assessments;
            if (request.SuccessProfileId.HasValue && request.SuccessProfileId.Value > 0)
            {
                // Lookup the additional details from the success profile
                assessments = await assessmentService.CalculateNewBehaviourDriversAndTraitsAsync(projectHubNorm, request.ExternalClientId, request.SuccessProfileId.Value, true);
            }
            else
            {
                assessments = BasicProjectHubAssessments();
            }

            // Populate the assessment information
            PopulateTypedProjectAssessment(a => a.Behavioural, a => assessments.Behavioural = a, request.Assessments.Competencies);
            PopulateMeasuredProjectAssessment(a => a.Traits, a => assessments.Traits = a, request.Assessments.Traits, AssessmentType.Kf4dTraits);
            PopulateMeasuredProjectAssessment(a => a.Drivers, a => assessments.Drivers = a, request.Assessments.Drivers, AssessmentType.Kf4dDrivers);
            PopulateMeasuredProjectAssessment(a => a.Experiences, a => assessments.Experiences = a, request.Assessments.Experiences, AssessmentType.Experiences);
            PopulateMeasuredProjectAssessment(a => a.Checking, a => assessments.Checking = a, request.Assessments.Checking, AssessmentType.AspectsChecking);
            PopulateTypedProjectAssessment(a => a.Numerical, a => assessments.Numerical = a, request.Assessments.Numerical);
            PopulateMeasuredProjectAssessment(a => a.Logical, a => assessments.Logical = a, request.Assessments.Logical, AssessmentType.ElementsLogical);
            PopulateTypedProjectAssessment(a => a.Verbal, a => assessments.Verbal = a, request.Assessments.Verbal);
            PopulateMeasuredProjectAssessment(a => a.AbstractReasoning, a => assessments.AbstractReasoning = a, request.Assessments.AbstractReasoning, AssessmentType.AbstractReasoning);
            PopulateMeasuredProjectAssessment(a => a.InclusiveLeaderSjt, a => assessments.InclusiveLeaderSjt = a, request.Assessments.InclusiveLeaderSJT, AssessmentType.InclusiveLeaderSjt);
            PopulateMeasuredProjectAssessment(a => a.Preferences, a => assessments.Preferences = a, request.Assessments.Preferences, AssessmentType.Preferences);
            await PopulateTechnicalSkillInventoryAsync(assessments, request);
            
            return new JRaw(assessments.ToJson());

            void PopulateTypedProjectAssessment<T>(Func<ProjectHubAssessments, T> getAssessmentPredicate, Action<T> setAssessmentPredicate, TypedProjectAssessment requestAssessment) where T : ProjectHubAssessment, new()
                => PopulateMeasuredProjectAssessment(getAssessmentPredicate, setAssessmentPredicate, requestAssessment, requestAssessment?.Type ?? AssessmentType.None);

            void PopulateMeasuredProjectAssessment<T>(Func<ProjectHubAssessments, T> getAssessmentPredicate, Action<T> setAssessmentPredicate, MeasuredProjectAssessment requestAssessment, AssessmentType assessmentType) where T : ProjectHubAssessment, new()
            {
                if (requestAssessment == null)
                {
                    return;
                }

                var hubAssessment = getAssessmentPredicate(assessments);

                if (hubAssessment == null)
                {
                    hubAssessment = new T();
                    setAssessmentPredicate(hubAssessment);
                }

                hubAssessment.Measure = requestAssessment.Measure;
                hubAssessment.AssessmentId = (int)assessmentType;
            }
        }

        private static ProjectHubAssessments BasicProjectHubAssessments()
        {
            const string dummyId = "pe";

            // Create an empty assessments object with default values.
            // This is required to pass validation and will be populated later when the success profile is defined.
            return new ProjectHubAssessments()
            {
                Behavioural = new Behavioural
                {
                    Report = AssessmentCompetencyShared.AllAllowedAssessmentCompetencies.ToList(),
                    Ranked = AssessmentCompetencyShared.AllAllowedAssessmentCompetencies.ToList(),
                },
                Traits = new Traits
                {
                    Report = new List<string> { dummyId },
                    Data = new List<Values> { new Values { Id = dummyId, Score = 0 } },
                },
                Drivers = new Drivers
                {
                    Report = new List<string> { dummyId }
                }
            };
        }

        private async Task PopulateTechnicalSkillInventoryAsync(ProjectHubAssessments assessments, KFOneProjectCreateUpdate request)
        {
            if (!(request.Assessments?.TechnicalSkillsInventory?.Measure ?? false))
            {
                return;
            }

            if (!request.SuccessProfileId.HasValue || request.SuccessProfileId.Value <= 0)
            {
                throw new HubException(HubErrorCode.InvalidRequestDetails, "Technical Skills Inventory requires a valid Success Profile ID.");
            }

            var successProfile = await ProductsApiService.GetSuccessProfileAsync(request.SuccessProfileId.Value, request.ExternalClientId).ConfigureAwait(false);

            assessments.TechnicalSkillsInventory = new TechnicalSkillsInventory
            {
                Measure = true,
                AssessmentId = (int)AssessmentType.TechnicalSkillsInventory,
                TechnicalCompetencies = GetTechnicalSkills()
            };

            List<TechnicalCompetency> GetTechnicalSkills()
            {
                return successProfile.Data.TechnicalSkillSection().AllSubCategories?.Select(c => new TechnicalCompetency
                {
                    Id = c.Id.ToString(),
                    Name = c.Description?.Name,
                    Description = c.Description?.DescriptionText,
                    Level = c.Description?.Level ?? 0,
                    TechnicalSkills = c.Dependents?.Select(ts => new TechnicalSkill
                    {
                        Id = ts.Id.ToString(),
                        Name = ts.Name
                    }).ToList() ?? new List<TechnicalSkill>(0)
                }).ToList();
            }
        }

        private string GetCommercialJson()
        {
            return new ProjectHubCommercial
            {
                ServiceType = ProjectHubServiceType.SelfService,
                EngagementNo = string.Empty
            }.ToJson();
        }

        private async Task<string> GetLocationJson(KFOneProjectCreateUpdate request)
        {
            var regionalNorms = await ProductsApiService.GetRegionalNormsAsync(request.ExternalClientId);

            var location = regionalNorms.Data.Locations.FirstOrDefault(l => string.Equals(l.CountryCode, request.Location, StringComparison.OrdinalIgnoreCase));

            if (location != null)
            {
                return new Location
                {
                    CountryId = location.CountryId,
                    IsNormCountryId = true
                }.ToJson();
            }

            throw new HubException(HubErrorCode.InvalidRequestDetails, $"Failed to find the location {request.Location}");
        }

        private async Task<ProjectHubNorm> GetNormAsync(string norm, int externalClientId)
        {
            if (string.IsNullOrEmpty(norm))
            {
                return null;
            }

            var regionalNorms = await ProductsApiService.GetRegionalNormsAsync(externalClientId);

            var normData = regionalNorms.Data.RegionalNorms.Where(n => string.Equals(n.Country, norm, StringComparison.OrdinalIgnoreCase)).OrderByDescending(n => n.Version).FirstOrDefault();

            if (normData != null)
            {
                return new ProjectHubNorm
                {
                    NormCountry = normData.Country,
                    NormId = normData.NormId,
                    NormVersion = normData.Version,
                };
            }

            throw new HubException(HubErrorCode.InvalidRequestDetails, $"Failed to find the norm {norm} for clientId - {externalClientId}");
        }       
    }
}
