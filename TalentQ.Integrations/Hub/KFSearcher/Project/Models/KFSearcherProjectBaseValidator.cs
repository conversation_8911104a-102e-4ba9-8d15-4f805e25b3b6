using FluentValidation;
using KornFerry.Services.Service.ProductsApi;
using System;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace TalentQ.Integrations.Hub.KFSearcher.Project.Models
{
    /// <summary>
    /// Base validator class for KFSearcher projects. Provides validation rules for project-related entities.
    /// </summary>
    /// <typeparam name="T">The type of the entity being validated.</typeparam>
    public abstract class KFSearcherProjectBaseValidator<T> : AbstractValidator<T>
        where T : class
    {
        protected readonly IProductsApiService productsApiService;

        /// <summary>
        /// Initializes a new instance of the <see cref="KFSearcherProjectBaseValidator{T}"/> class.
        /// </summary>
        /// <param name="productsApiService">The products API service to use for validation.</param>
        protected KFSearcherProjectBaseValidator(IProductsApiService productsApiService)
        {
            this.productsApiService = productsApiService ?? new ProductsApiServiceCached();
        }

        /// <summary>
        /// Adds a validation rule for the admin email.
        /// </summary>
        /// <param name="getAdminEmail">A function to retrieve the admin email from the entity.</param>
        protected void AddAdminEmailRule(Func<T, string> getAdminEmail)
        {
            RuleFor(model => getAdminEmail(model))
                .NotEmpty().WithMessage("Must provide an admin email")
                .MustAsync(async (model, email, _) =>
                {
                    try
                    {
                        var userInfo = await productsApiService.GetUserByEmailAsync(email);
                        return string.Equals(userInfo?.Email, email, StringComparison.OrdinalIgnoreCase);
                    }
                    catch (Exception)
                    {
                        // If an exception occurs, we assume the profile does not exist.
                        return false;
                    }
                })
                .WithMessage(model => $"Invalid AdminUser {getAdminEmail(model)}, unable to find User");
        }

        /// <summary>
        /// Adds a validation rule for the external client ID.
        /// </summary>
        /// <param name="getExternalClientId">A function to retrieve the external client ID from the entity.</param>
        protected void AddExternalClientIdRule(Func<T, int> getExternalClientId)
        {
            RuleFor(model => getExternalClientId(model))
                .GreaterThan(0).WithMessage("Must provide a client id to associate this project to");
        }

        /// <summary>
        /// Adds a validation rule for the success profile ID.
        /// </summary>
        /// <param name="getSuccessProfileId">A function to retrieve the success profile ID from the entity.</param>
        /// <param name="getExternalClientId">A function to retrieve the external client ID from the entity.</param>
        protected void AddSuccessProfileIdRule(Func<T, int> getSuccessProfileId, Func<T, int> getExternalClientId)
        {
            RuleFor(model => new { SuccessProfileId = getSuccessProfileId(model), ClientId = getExternalClientId(model) })
                .Must(x => x.SuccessProfileId > 0)
                .WithMessage("Must provide a success profile id greater than 0")
                .MustAsync(async (model, x, _) =>
                {
                    try { 
                        var successProfile = await productsApiService.GetSuccessProfileAsync(x.SuccessProfileId, x.ClientId);
                        return successProfile != null;
                    }
                    catch (Exception)
                    {
                        // If an exception occurs, we assume the profile does not exist.
                        return false;
                    }
                })
                .WithMessage(model => $"Invalid SuccessProfileId {getSuccessProfileId(model)} for ClientId {getExternalClientId(model)}, unable to find SuccessProfile");
        }

        /// <summary>
        /// Adds validation rules for SPS details.
        /// </summary>
        /// <param name="getSps">A function to retrieve the SPS details from the entity.</param>
        protected void AddSpsRules(Func<T, SPS> getSps)
        {
            RuleFor(model => getSps(model))
                .Cascade(CascadeMode.StopOnFirstFailure)
                .NotNull().WithMessage("Must provide SPS details for this project")
                .DependentRules(() =>
                {
                    RuleFor(model => getSps(model).Required)
                        .NotNull().WithMessage("SPS must have a Required value");

                    When(model => getSps(model)?.Required == true, () =>
                    {
                        RuleFor(model => getSps(model).SurveyName)
                            .NotEmpty().WithMessage("SPS SurveyName cannot be empty");

                        RuleFor(model => getSps(model).Deadline)
                            .NotEmpty().WithMessage("SPS Deadline cannot be empty")
                            .Must(BeAValidEpoch).WithMessage("SPS Deadline is not a valid Unix time.");

                        RuleFor(model => getSps(model).PopulateFromSuccessProfile)
                            .NotNull().WithMessage("PopulateFromSuccessProfile must be true/false");

                        RuleFor(model => getSps(model).Modules)
                            .NotEmpty().WithMessage("SPS Modules cannot be empty")
                            .DependentRules(() =>
                            {
                                RuleFor(model => getSps(model).Modules.Competencies)
                                    .NotNull().WithMessage("SPS Modules Competencies cannot be null");

                                RuleFor(model => getSps(model).Modules.CultureSort)
                                    .NotNull().WithMessage("SPS Modules CultureSort cannot be null");

                                RuleFor(model => getSps(model).Modules.JobAnalysis)
                                    .NotNull().WithMessage("SPS Modules JobAnalysis cannot be null");

                                RuleFor(model => getSps(model).Modules)
                                    .Must(modules =>
                                        (modules.Competencies == true) ||
                                        (modules.CultureSort == true) ||
                                        (modules.JobAnalysis == true))
                                    .WithMessage("At least one SPS Module (Competencies, CultureSort, or JobAnalysis) must be true.");
                            });
                    });
                });
        }

        /// <summary>
        /// Validates whether a given Unix timestamp (in seconds) falls within a reasonable date range.
        /// </summary>
        /// <param name="timestamp">Unix timestamp in seconds.</param>
        /// <returns>True if the timestamp is between the years 2000 and 2100; otherwise, false.</returns>
        public bool BeAValidEpoch(long? timestamp)
        {
            if (timestamp == null)
            {
                return false;
            }

            var date = DateTimeOffset.FromUnixTimeSeconds((long)timestamp);
            return date.Year >= 2000 && date.Year <= 2100;
        }
    }
}
