﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{D5634814-E0D7-475E-8538-01EFBD2BBC4F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TalentQ.Integrations</RootNamespace>
    <AssemblyName>TalentQ.Integrations</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>..\TQASStylecop.ruleset</CodeAnalysisRuleSet>
    <DocumentationFile>bin\Debug\TalentQ.Integrations.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>..\TQASStylecop.ruleset</CodeAnalysisRuleSet>
    <DocumentationFile>bin\Release\TalentQ.Integrations.xml</DocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Aspose.Cells, Version=7.3.0.0, Culture=neutral, PublicKeyToken=716fcc553a201e56, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Libraries\Aspose.Cells\net3.5_ClientProfile\Aspose.Cells.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper, Version=7.0.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.7.0.1\lib\net45\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.Core, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604" />
    <Reference Include="ceTe.DynamicPDF.40, Version=8.0.0.40, Culture=neutral, PublicKeyToken=09b5ce0d5c0a9d8b, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Libraries\ceTe.DynamicPDF.v8\ceTe.DynamicPDF.40.dll</HintPath>
    </Reference>
    <Reference Include="FluentValidation, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7de548da2fbae0f0, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentValidation.8.0.100\lib\net45\FluentValidation.dll</HintPath>
    </Reference>
    <Reference Include="FluentValidation.ValidatorAttribute, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7de548da2fbae0f0, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentValidation.ValidatorAttribute.8.0.100\lib\net45\FluentValidation.ValidatorAttribute.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=4.2.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.4.2.2\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=2.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.2.1.0\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.7.15\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=106.15.0.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.106.15.0\lib\net452\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.4.4.1\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.ComponentModel.Primitives, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Primitives.4.3.0\lib\net45\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Http, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TQIntegrationSOAP\Bin\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AutoMapperConfiguration.cs" />
    <Compile Include="General\Models\StreamFileResult.cs" />
    <Compile Include="General\Postback\CallbackService.cs" />
    <Compile Include="General\Postback\Communications\IPostBackCommunication.cs" />
    <Compile Include="General\Postback\Communications\PostbackCommunicationBase.cs" />
    <Compile Include="General\Postback\Communications\Rest\KornFerryAwsAPiAuthenticator.cs" />
    <Compile Include="General\Postback\Communications\Rest\NewtonsoftJsonSerializer.cs" />
    <Compile Include="General\Postback\Communications\Rest\NoAuthenticator.cs" />
    <Compile Include="General\Postback\Communications\Rest\PalmsAuthenticator.cs" />
    <Compile Include="General\Postback\Communications\Soap\ISoapApiAuthenticator.cs" />
    <Compile Include="General\Postback\Communications\Soap\SoapHeaderAuthenticator.cs" />
    <Compile Include="General\Postback\Communications\Soap\SoapCommunication.cs" />
    <Compile Include="General\Postback\Communications\Rest\RestCommunication.cs" />
    <Compile Include="General\Postback\Communications\Rest\RestParameterAuthenticator.cs" />
    <Compile Include="General\Postback\ClientPostbackConfigurationRetriever.cs" />
    <Compile Include="General\Postback\DataHandlers\AvatureImportBuilder.cs" />
    <Compile Include="General\Postback\DataHandlers\IC2AssessmentBuilder.cs" />
    <Compile Include="General\Postback\DataHandlers\BlendedDefaultBuilder.cs" />
    <Compile Include="General\Postback\DataHandlers\IDataBuilder.cs" />
    <Compile Include="General\Postback\DataHandlers\KFSearcherBuilder.cs" />
    <Compile Include="General\Postback\DataHandlers\V2DefaultBuilder.cs" />
    <Compile Include="General\Postback\DbPostbackRepository.cs" />
    <Compile Include="General\Postback\Filters\IPostbackFilter.cs" />
    <Compile Include="General\Postback\Filters\PostbackFilterClientSettingICEnvironment.cs" />
    <Compile Include="General\Postback\Filters\PostbackFilterKFAssess2.cs" />
    <Compile Include="General\Postback\Filters\PostbackFilterCandidateExMetadata.cs" />
    <Compile Include="General\Postback\Filters\PostbackFilterLatestSuccessProfile.cs" />
    <Compile Include="General\Postback\Models\FilterType.cs" />
    <Compile Include="General\Postback\Models\CommunicationPostbackResult.cs" />
    <Compile Include="General\Postback\Models\IntegrationPostbackCallbackLog.cs" />
    <Compile Include="General\Postback\Models\IntegrationPostbackConfig.cs" />
    <Compile Include="General\Postback\IPostBackRepository.cs" />
    <Compile Include="General\Postback\Models\IntegrationPostbackLog.cs" />
    <Compile Include="General\Postback\Models\IntegrationPostbackFilter.cs" />
    <Compile Include="General\Postback\Models\PostbackContext.cs" />
    <Compile Include="General\Postback\Models\PostbackEvent.cs" />
    <Compile Include="General\Postback\Models\PostbackFilterType.cs" />
    <Compile Include="General\Postback\Models\StatusRequest.cs" />
    <Compile Include="General\Postback\Filters\PostbackFilterPlatform.cs" />
    <Compile Include="General\Postback\PostbackFilterFactory.cs" />
    <Compile Include="General\Postback\PostbackHelpers.cs" />
    <Compile Include="General\Postback\PostbackService.cs" />
    <Compile Include="General\Helpers\TypeHelper.cs" />
    <Compile Include="General\Postback\RequestBuilders\IRequestBuilder.cs" />
    <Compile Include="General\Postback\RequestBuilders\JsonXmlRequestBuilder.cs" />
    <Compile Include="General\Postback\RequestBuilders\MultipartFormRequestBuilder.cs" />
    <Compile Include="General\Postback\RequestBuilders\TemplateRequestBuilder.cs" />
    <Compile Include="GlobalSuppressions.cs" />
    <Compile Include="HayGroup\HayGroupReportOperation.cs" />
    <Compile Include="Hub\Admin\Branding\Models\AssessmentIconSetting.cs" />
    <Compile Include="Hub\Admin\Branding\Models\BrandingIdNameModel.cs" />
    <Compile Include="Hub\Admin\Branding\Models\CandidateDashboardBrandingSection.cs" />
    <Compile Include="Hub\Admin\Branding\Models\CandidateSignInBrandingSection.cs" />
    <Compile Include="Hub\Admin\Branding\Models\GeneralBrandingSection.cs" />
    <Compile Include="Hub\Admin\Branding\Models\ParticipantPortalBrandingModel.cs" />
    <Compile Include="Hub\Admin\Branding\Models\ParticipantPortalBrandingSettings.cs" />
    <Compile Include="Hub\KFOne\Project\Models\KFOneSuccessProfileUpdateValidator.cs" />
    <Compile Include="Hub\KFOne\Project\Models\KFOneSuccessProfileUpdate.cs" />
    <Compile Include="Hub\KFOne\Reports\AvailableReports\KFOneReportAvailabilityService.cs" />
    <Compile Include="Hub\KFOne\Reports\AvailableReports\Models\KFOneReportAvailabilityRequest.cs" />
    <Compile Include="Hub\KFOne\Reports\AvailableReports\Models\KFOneReportAvailabilityRequestValidator.cs" />
    <Compile Include="Hub\KFOne\Reports\AvailableReports\Models\KFOneReportAvailabilityResponse.cs" />
    <Compile Include="Hub\KFSearcher\Deeplink\KFSearcherDeeplinkService.cs" />
    <Compile Include="Hub\KFSearcher\Deeplink\Models\KFSearcherDeeplink.cs" />
    <Compile Include="Hub\KFSearcher\Deeplink\Models\KFSearcherDeeplinkResponse.cs" />
    <Compile Include="Hub\KFSearcher\Deeplink\Models\KFSearcherDeeplinkValidator.cs" />
    <Compile Include="Hub\KFSearcher\Project\Models\KFSearcherProjectAdditionalSPsValidator.cs" />
    <Compile Include="Hub\KFSearcher\Project\KFSearcherProjectAdditionalSPsService.cs" />
    <Compile Include="Hub\KFSearcher\Project\KFSearcherProjectCreateUpdateService.cs" />
    <Compile Include="Hub\KFSearcher\Project\Models\KFSearcherProjectAdditionalSPs.cs" />
    <Compile Include="Hub\KFSearcher\Project\Models\KFSearcherProjectBaseValidator.cs" />
    <Compile Include="Hub\KFSearcher\Project\Models\KFSearcherProjectCreateUpdate.cs" />
    <Compile Include="Hub\KFSearcher\Project\Models\KFSearcherProjectCreateUpdateResponse.cs" />
    <Compile Include="Hub\KFSearcher\Project\Models\KFSearcherProjectCreateUpdateValidator.cs" />
    <Compile Include="Hub\KFOne\TQUserExtensions.cs" />
    <Compile Include="Hub\KFSearcher\Participant\KFSearcherParticipantService.cs" />
    <Compile Include="Hub\KFSearcher\Participant\Models\KFSearcherParticipantCreateUpdate.cs" />
    <Compile Include="Hub\KFSearcher\Participant\Models\KFSearcherParticipantCreateUpdateResponse.cs" />
    <Compile Include="Hub\KFSearcher\Participant\Models\KFSearcherParticipantCreateUpdateValidator.cs" />
    <Compile Include="Hub\KFSearcher\Participant\Models\KFSearcherParticipantResponse.cs" />
    <Compile Include="Hub\Misc\DeeplinkSettings.cs" />
    <Compile Include="Hub\Misc\TQUserExtensions.cs" />
    <Compile Include="Hub\KFSearcher\Participant\Models\KFSearcherParticipantSearch.cs" />
    <Compile Include="Hub\KFSearcher\Participant\Models\KFSearcherParticipantSearchResponse.cs" />
    <Compile Include="Hub\KFSearcher\Participant\Models\KFSearcherParticipantSearchValidator.cs" />
    <Compile Include="Hub\Models\ParticipantRemovalDetail.cs" />
    <Compile Include="Hub\Admin\Branding\Models\ReflexAssessmentManifestSetting.cs" />
    <Compile Include="Hub\Admin\Branding\ParticipantPortalBrandingService.cs" />
    <Compile Include="Hub\Admin\Clients\Models\BlendedReportLanguages.cs" />
    <Compile Include="Hub\Admin\Clients\Models\ClientAssessmentNormsModel.cs" />
    <Compile Include="Hub\Admin\Clients\Models\ClientCustomNormDescriptions.cs" />
    <Compile Include="Hub\Admin\Clients\Models\ClientCustomNormSaveModel.cs" />
    <Compile Include="Hub\Admin\Clients\Models\ClientReportEnableUpdateModel.cs" />
    <Compile Include="Hub\Admin\Clients\Models\ReportDisplayOrderUpdateModel.cs" />
    <Compile Include="Hub\Admin\Clients\Models\ReportFilterConfigurationOptions.cs" />
    <Compile Include="Hub\Admin\Clients\Models\ReportFilterModel.cs" />
    <Compile Include="Hub\Admin\Clients\Services\ClientAssessmentValidityPeriodService.cs" />
    <Compile Include="Hub\Admin\Clients\Services\ClientConfigurableReports.cs" />
    <Compile Include="Hub\Admin\Clients\Services\ClientNormService.cs" />
    <Compile Include="Hub\Admin\Clients\Services\ClientReportConfigurations.cs" />
    <Compile Include="Hub\Admin\Clients\Services\ClientReportsDisplyOrder.cs" />
    <Compile Include="Hub\Admin\Clients\Services\ClientSearch.cs" />
    <Compile Include="Hub\Admin\Clients\Models\BlendedReportWithConfiguration.cs" />
    <Compile Include="Hub\Admin\Clients\Models\ClientSearchResult.cs" />
    <Compile Include="Hub\Admin\Clients\Services\ClientService.cs" />
    <Compile Include="Hub\Admin\Clients\Services\IClientReportConfigurations.cs" />
    <Compile Include="Hub\Admin\Clients\Services\ReportFilterHelper.cs" />
    <Compile Include="Hub\Admin\Clients\Services\ReportFilterService.cs" />
    <Compile Include="Hub\Admin\Extracts\ExtractRequestService.cs" />
    <Compile Include="Hub\Admin\Extracts\Request.cs" />
    <Compile Include="Hub\Admin\Extracts\RequestMappings.cs" />
    <Compile Include="Hub\Admin\Participants\ParticipantReportService.cs" />
    <Compile Include="Hub\Admin\Participants\ReportRegenerationRequest.cs" />
    <Compile Include="Hub\Admin\Projects\IProjectReportService.cs" />
    <Compile Include="Hub\Admin\Projects\Models\ClientProjectsResult.cs" />
    <Compile Include="Hub\Admin\Projects\Models\CustomProjectTypeHistoryViewModel.cs" />
    <Compile Include="Hub\Admin\Projects\Models\CustomProjectTypeResponseModel.cs" />
    <Compile Include="Hub\Admin\Projects\Models\NormAndLocationResult.cs" />
    <Compile Include="Hub\Admin\Projects\Models\ProjectParticipantModel.cs" />
    <Compile Include="Hub\Admin\Projects\Models\ProjectReportRegenerateModel.cs" />
    <Compile Include="Hub\Admin\Projects\Models\ProjectSummary.cs" />
    <Compile Include="Hub\Admin\Projects\NormAndLocationService.cs" />
    <Compile Include="Hub\Admin\Projects\ProjectReportsService.cs" />
    <Compile Include="Hub\Admin\Reports\Branding\Branding.cs" />
    <Compile Include="Hub\Admin\Reports\Branding\BrandingPreviewService.cs" />
    <Compile Include="Hub\Admin\Reports\Branding\Models\BrandingDetailsModel.cs" />
    <Compile Include="Hub\Admin\Reports\Branding\Models\ClientBrandingModel.cs" />
    <Compile Include="Hub\Admin\Reports\Branding\Models\PreviewRequestModel.cs" />
    <Compile Include="Hub\Admin\Reports\Branding\Models\ConfigurableReportModel.cs" />
    <Compile Include="Hub\Admin\Reports\Configurable\ConfigurableReports.cs" />
    <Compile Include="Hub\Admin\Reports\Configurable\ConfigurableReportOptions.cs" />
    <Compile Include="Hub\Admin\Reports\Configurable\IConfigurableReports.cs" />
    <Compile Include="Hub\Admin\Reports\Configurable\IConfigurableReportOptions.cs" />
    <Compile Include="Hub\Admin\Reports\Configurable\Models\ConfigurableReportModel.cs" />
    <Compile Include="Hub\Admin\Reports\Configurable\Models\ConfigurableReportModelBase.cs" />
    <Compile Include="Hub\Admin\Reports\Configurable\Models\ReportOptions.cs" />
    <Compile Include="Hub\Admin\Reports\Configurable\Models\AddReportRequest.cs" />
    <Compile Include="Hub\Admin\Reports\Configurable\Models\UpdateReportRequest.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\IProjectOptions.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\IReportPreview.cs" />
    <Compile Include="Hub\Admin\Reports\Assets\Models\AssetUploadResult.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\Models\ProjectOptions.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\Models\ReportPreviewOptions.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\Models\ReportPreviewRequest.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\PotentialProjectOptions.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\ProjectOptions.cs" />
    <Compile Include="Hub\Admin\Reports\Assets\ClientReportAssets.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\ProjectOptionsFactory.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\ReportPreviewBase.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\ReportPreviewFactory.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\ReportPreviewOptions.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\PotentialReportPreview.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\ReportPreview.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\ReportSuccessProfiles.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\SJTProjectOptions.cs" />
    <Compile Include="Hub\Admin\Reports\ReportPreview\SJTReportPreview.cs" />
    <Compile Include="Hub\Admin\RescoreService.cs" />
    <Compile Include="Hub\Admin\Shared\LanguageExtensions.cs" />
    <Compile Include="Hub\Admin\Shared\LanguageModel.cs" />
    <Compile Include="Hub\Admin\Users\Models\AccessPolicies.cs" />
    <Compile Include="Hub\Admin\Users\AccessPolicyService.cs" />
    <Compile Include="Hub\Admin\Users\IAccessPolicyService.cs" />
    <Compile Include="Hub\AssessmentService.cs" />
    <Compile Include="Hub\Authorization\IAuthKfasClient.cs" />
    <Compile Include="Hub\Authorization\IAuthUsers.cs" />
    <Compile Include="Hub\CandidateScoreService.cs" />
    <Compile Include="Hub\ICAssessment\Migration\Models\EmployeeReportData.cs" />
    <Compile Include="Hub\ICAssessment\Migration\Repositories\IEmployeeReportDbRepository.cs" />
    <Compile Include="Hub\ICAssessment\Migration\Repositories\EmployeeReportDbRepository.cs" />
    <Compile Include="Hub\ICAssessment\Models\ParticipantValidateResponse.cs" />
    <Compile Include="Hub\ICAssessment\ParticipantICCreateService.cs" />
    <Compile Include="Hub\CandidateMatchService.cs" />
    <Compile Include="Hub\Authorization\IAuthCandidates.cs" />
    <Compile Include="Hub\Authorization\IAuthProjects.cs" />
    <Compile Include="Hub\ClientCandidateSearchService.cs" />
    <Compile Include="Hub\CandidateRemovalService.cs" />
    <Compile Include="Hub\CandidateSearchMetaData.cs" />
    <Compile Include="Hub\CandidateProjectSearchService.cs" />
    <Compile Include="Hub\CandidateService.cs" />
    <Compile Include="Hub\CandidateBatchUploadService.cs" />
    <Compile Include="Hub\ClientReportService.cs" />
    <Compile Include="Hub\ClientReportServiceCached.cs" />
    <Compile Include="Hub\ClientService.cs" />
    <Compile Include="Hub\Admin\Projects\Models\CustomProjectTypeRequestModel.cs" />
    <Compile Include="Hub\CustomProjectTypeHelper.cs" />
    <Compile Include="Hub\CustomProjectTypeService.cs" />
    <Compile Include="Hub\CustomProjectTypeServiceCached.cs" />
    <Compile Include="Hub\DashboardService.cs" />
    <Compile Include="Hub\EmailService.cs" />
    <Compile Include="Hub\ExtractService.cs" />
    <Compile Include="Hub\GraphQlService.cs" />
    <Compile Include="Hub\ICAssessment\AssessmentDataService.cs" />
    <Compile Include="Hub\ICAssessment\ICStatusExtensions.cs" />
    <Compile Include="Hub\ICAssessment\Migration\EmployeeReportExcelService.cs" />
    <Compile Include="Hub\ICAssessment\Models\AssessmentCompositeId.cs" />
    <Compile Include="Hub\ICAssessment\Models\CompleteAssessmentRequest.cs" />
    <Compile Include="Hub\ICAssessment\Models\ParticipantReportRequest.cs" />
    <Compile Include="Hub\ICAssessment\Models\StatusResponse.cs" />
    <Compile Include="Hub\ICAssessment\ParticipantStatusService.cs" />
    <Compile Include="Hub\ICAssessment\ParticipantReportService.cs" />
    <Compile Include="Hub\ICAssessment\Migration\ProjectMigrationService.cs" />
    <Compile Include="Hub\IGraphQlService.cs" />
    <Compile Include="Hub\Misc\PhotoDownloadSettings.cs" />
    <Compile Include="Hub\PhotoProcessingService.cs" />
    <Compile Include="Hub\Models\PhotoUpload\PhotoUploadContext.cs" />
    <Compile Include="Hub\KFOne\Assessment\KFOneAssessmentService.cs" />
    <Compile Include="Hub\KFOne\Assessment\Models\KFOneAssessmentLaunchResponse.cs" />
    <Compile Include="Hub\KFOne\Participant\KFOneParticipantService.cs" />
    <Compile Include="Hub\KFOne\Participant\Models\KFOneParticipantCreateUpdate.cs" />
    <Compile Include="Hub\KFOne\Participant\Models\KFOneParticipantResponse.cs" />
    <Compile Include="Hub\KFOne\Participant\Models\KFOneParticipantCreateUpdateValidator.cs" />
    <Compile Include="Hub\KFOne\Project\KFOneProjectCreateUpdateService.cs" />
    <Compile Include="Hub\KFOne\Project\Models\KFOneProjectCreateUpdateResponse.cs" />
    <Compile Include="Hub\KFOne\Project\Models\KFOneProjectCreateUpdate.cs" />
    <Compile Include="Hub\KFOne\Project\Models\KFOneProjectCreateUpdateValidator.cs" />
    <Compile Include="Hub\Misc\AssessmentCompetencyCustomChecker.cs" />
    <Compile Include="Hub\Misc\AssessmentCompetencySanitiser.cs" />
    <Compile Include="Hub\Misc\ClientCandidateSearchContextCreator.cs" />
    <Compile Include="Hub\Misc\CandidateProjectSearchContextCreator.cs" />
    <Compile Include="Hub\Misc\Demographics\IDemographicsService.cs" />
    <Compile Include="Hub\Misc\Demographics\NoReuseDemographicsService.cs" />
    <Compile Include="Hub\Misc\HubFileNameCreator.cs" />
    <Compile Include="Hub\Misc\HubException.cs" />
    <Compile Include="Hub\Misc\HubFileNameCreatorBase.cs" />
    <Compile Include="Hub\Misc\Demographics\ReuseDemographicsService.cs" />
    <Compile Include="Hub\Misc\JobClientAdapter.cs" />
    <Compile Include="Hub\Misc\MemoryLoggerHelper.cs" />
    <Compile Include="Hub\Misc\NonCandidateHubFileNameCreator.cs" />
    <Compile Include="Hub\Misc\ProductHubSettings.cs" />
    <Compile Include="Hub\Misc\ProductsHubMoreDetailsToken.cs" />
    <Compile Include="Hub\Misc\ProjectHubAssessmentsMerger.cs" />
    <Compile Include="Hub\Misc\ProjectHubHelper.cs" />
    <Compile Include="Hub\Misc\ProjectSearchContextCreator.cs" />
    <Compile Include="Hub\Misc\ResultJsonConverter.cs" />
    <Compile Include="Hub\Misc\RetryHelper.cs" />
    <Compile Include="Hub\Misc\ScheduleAdapter.cs" />
    <Compile Include="Hub\Misc\UrlHelper.cs" />
    <Compile Include="Hub\Models\Admin\RescoreRequest.cs" />
    <Compile Include="Hub\Models\Assessment\AdditionalTimeRequest.cs" />
    <Compile Include="Hub\Models\Assessment\CompleteAssessmentRequest.cs" />
    <Compile Include="Hub\Models\Assessment\ResetRequest.cs" />
    <Compile Include="Hub\Models\Assessment\ReenableRequest.cs" />
    <Compile Include="Hub\Models\Assessment\ResetResult.cs" />
    <Compile Include="Hub\Models\Candidate\AssessmentModel.cs" />
    <Compile Include="Hub\Models\Candidate\AssignProjectRequest.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateClientSearchResult.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateCreateBatchResults.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateHiredBatch.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateCreateBatch.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateMatchRequest.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateMatchResult.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateReportResult.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateCreateResult.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateModel.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateDetailsResult.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateResult.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateSearchRequest.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateSearchResult.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateSearchSimpleResults.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateSearchSimpleResult.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateStatusUpdate.cs" />
    <Compile Include="Hub\Models\Candidate\MoreDetailsResult.cs" />
    <Compile Include="Hub\Models\Candidate\ParticipantBulkCreate.cs" />
    <Compile Include="Hub\Models\ClientModel.cs" />
    <Compile Include="Hub\Models\ClientSearchModel.cs" />
    <Compile Include="Hub\Models\Client\ClientAssessmentValidityPeriodsModel.cs" />
    <Compile Include="Hub\Models\Client\ClientMetadataResult.cs" />
    <Compile Include="Hub\Models\Client\EmailTemplateHeader.cs" />
    <Compile Include="Hub\Models\Dashboard\DashboardModel.cs" />
    <Compile Include="Hub\Models\Proctoring\ProctoringCallbackRequest.cs" />
    <Compile Include="Hub\Models\Reports\DfrLaunchRequestModel.cs" />
    <Compile Include="Hub\Models\EmailSetting\ByDateConfig.cs" />
    <Compile Include="Hub\Models\EmailSetting\ByRecurringConfig.cs" />
    <Compile Include="Hub\Models\EmailSetting\Comparers\ByRecurringConfigEqualityComparer.cs" />
    <Compile Include="Hub\Models\EmailSetting\Comparers\ByDateConfigEqualityComparer.cs" />
    <Compile Include="Hub\Models\EmailSetting\Comparers\ParticipantSendConfigEqualityComparer.cs" />
    <Compile Include="Hub\Models\EmailSetting\DateTimeWithTimeZoneModel.cs" />
    <Compile Include="Hub\Models\EmailSetting\Comparers\EmailSettingIdComparer.cs" />
    <Compile Include="Hub\Models\EmailSetting\Comparers\EmailSettingModelEqualityComparer.cs" />
    <Compile Include="Hub\Models\EmailSetting\EmailSettingModel.cs" />
    <Compile Include="Hub\Models\EmailSetting\EmailSettingCreateUpdate.cs" />
    <Compile Include="Hub\Models\EmailSetting\EmailSettingCreateUpdateResult.cs" />
    <Compile Include="Hub\Models\EmailSetting\ParticipantSendConfig.cs" />
    <Compile Include="Hub\Models\EmailSetting\RecurringEndDateModel.cs" />
    <Compile Include="Hub\Models\EmailSetting\RecurringMonthModel.cs" />
    <Compile Include="Hub\Models\EmailSetting\RecurringWeekModel.cs" />
    <Compile Include="Hub\Models\Email\EmailLogResult.cs" />
    <Compile Include="Hub\Models\Email\EmailTemplate.cs" />
    <Compile Include="Hub\Models\Email\EmailTemplateCreateUpdate.cs" />
    <Compile Include="Hub\Models\Email\EmailTemplateCreateUpdateResult.cs" />
    <Compile Include="Hub\Models\Email\EmailTemplateLanguage.cs" />
    <Compile Include="Hub\Models\ExternalUserModel.cs" />
    <Compile Include="Hub\Models\Extracts\ExtractLanguages.cs" />
    <Compile Include="Hub\Models\Extracts\ExtractRequest.cs" />
    <Compile Include="Hub\Models\Extracts\ExtractScoreFilterOptions.cs" />
    <Compile Include="Hub\Models\Extracts\ExtractServiceResult.cs" />
    <Compile Include="Hub\Models\GraphQl\GraphQlVariablesConverter.cs" />
    <Compile Include="Hub\Models\GraphQl\LambdaRequestModel.cs" />
    <Compile Include="Hub\Models\GraphQl\QueryRequestModel.cs" />
    <Compile Include="Hub\Models\LocaleModel.cs" />
    <Compile Include="Hub\Models\MetadataResult.cs" />
    <Compile Include="Hub\Models\Candidate\CandidateCreateUpdate.cs" />
    <Compile Include="Hub\Models\ProjectHubCandidateReportDFR.cs" />
    <Compile Include="Hub\Models\Project\CustomProjectTypeListResponse.cs" />
    <Compile Include="Hub\Models\Project\ProjectUpdateRequestModel.cs" />
    <Compile Include="Hub\Models\Project\NormChangePreview.cs" />
    <Compile Include="Hub\Models\Project\ProjectDetailsResult.cs" />
    <Compile Include="Hub\Models\Reports\ReportDataResponse.cs" />
    <Compile Include="Hub\Models\Reports\ReportReleaseRequest.cs" />
    <Compile Include="Hub\Models\Reports\ReportDataRequestModel.cs" />
    <Compile Include="Hub\Models\Sanctions\SanctionsAuditModel.cs" />
    <Compile Include="Hub\Models\Sanctions\SanctionsConfirmationModel.cs" />
    <Compile Include="Hub\Models\Sanctions\SanctionsConfirmationResult.cs" />
    <Compile Include="Hub\EmailSettingService.cs" />
    <Compile Include="Hub\Models\Score\NormInfo.cs" />
    <Compile Include="Hub\Models\Score\NormName.cs" />
    <Compile Include="Hub\Models\Score\ScoreCategory.cs" />
    <Compile Include="Hub\Models\Score\ScoreGroup.cs" />
    <Compile Include="Hub\Models\Score\ScoreHeader.cs" />
    <Compile Include="Hub\Models\Score\ScoreItem.cs" />
    <Compile Include="Hub\Models\SuccessProfile\SuccessProfilePublishedModel.cs" />
    <Compile Include="Hub\Models\SuccessProfile\SuccessProfileUsageSearchModel.cs" />
    <Compile Include="Hub\Misc\PhotoUploadSettings.cs" />
    <Compile Include="Hub\ProctoringService.cs" />
    <Compile Include="Hub\ProjectAssessmentService.cs" />
    <Compile Include="Hub\ProjectDeleteService.cs" />
    <Compile Include="Hub\ProjectSearchMetadata.cs" />
    <Compile Include="Hub\DfrService.cs" />
    <Compile Include="Hub\ServiceInterfaces\ICustomProjectTypeService.cs" />
    <Compile Include="Hub\ServiceInterfaces\IDrfService.cs" />
    <Compile Include="Hub\ServiceInterfaces\IScheduleAdapter.cs" />
    <Compile Include="Hub\ServiceInterfaces\IJobClientAdapter.cs" />
    <Compile Include="Hub\Repositories\ISanctionsRepository.cs" />
    <Compile Include="Hub\Repositories\SanctionsDbRepository.cs" />
    <Compile Include="Hub\SanctionsAuditService.cs" />
    <Compile Include="Hub\Validators\BicOverridesValidator.cs" />
    <Compile Include="Hub\Validators\DfrLaunchRequestValidator.cs" />
    <Compile Include="Hub\Validators\CustomProjectTypeCreateUpdateValidation.cs" />
    <Compile Include="Hub\Validators\ClientAssessmentValidityPeriodValidator.cs" />
    <Compile Include="Hub\Validators\CompetencyListValidator.cs" />
    <Compile Include="Hub\Repositories\HubCandidateDbRepository.cs" />
    <Compile Include="Hub\Repositories\ICandidateRepository.cs" />
    <Compile Include="Hub\Validators\CustomNormDescriptionValidator.cs" />
    <Compile Include="Hub\Validators\CustomProjectTypeProjectHubAssessmentsValidator.cs" />
    <Compile Include="Hub\Validators\EmailSetting\DateTimeWithTimeZoneModelValidator.cs" />
    <Compile Include="Hub\Validators\EmailSetting\EmailSettingModelValidator.cs" />
    <Compile Include="Hub\Validators\EmailSetting\RecurrenceIntervalModelValidator.cs" />
    <Compile Include="Hub\Validators\EmailSetting\RecurringEndModelValidator.cs" />
    <Compile Include="Hub\Validators\EmailSetting\RecurringMonthModelValidator.cs" />
    <Compile Include="Hub\Validators\EmailSetting\RecurringWeekModelValidator.cs" />
    <Compile Include="Hub\Validators\EmailSetting\ScheduleByDateConfigValidator.cs" />
    <Compile Include="Hub\Validators\EmailSetting\EmailSettingCreateUpdateValidator.cs" />
    <Compile Include="Hub\Validators\EmailSetting\ScheduleByRecurrenceConfigValidator.cs" />
    <Compile Include="Hub\Validators\EmailSetting\ParticipantSendConfigValidator.cs" />
    <Compile Include="Hub\Validators\EmailSetting\SendDateModelValidator.cs" />
    <Compile Include="Hub\Validators\NormValidator.cs" />
    <Compile Include="Hub\Validators\ReportReleaseRequestValidator.cs" />
    <Compile Include="Hub\Validators\SanctionAuditModelValidator.cs" />
    <Compile Include="Hub\Validators\SanctionsConfirmationModelValidator.cs" />
    <Compile Include="Hub\Validators\SuccessProfilePublishRequestValidator.cs" />
    <Compile Include="Hub\Validators\ValidatorExtensions.cs" />
    <Compile Include="Hub\Validators\ProjectEmailModelValidator.cs" />
    <Compile Include="Hub\Validators\ReportAvailabilityRequestValidator.cs" />
    <Compile Include="Hub\Models\Project\ReportAvailabilityResult.cs" />
    <Compile Include="Hub\Models\Project\ReportAvailabilityRequest.cs" />
    <Compile Include="Hub\Models\Project\EmailModel.cs" />
    <Compile Include="Hub\Models\Project\ProjectAdditionalSPs.cs" />
    <Compile Include="Hub\Models\Project\ProjectCreateUpdate.cs" />
    <Compile Include="Hub\Models\Project\ProjectCreateUpdateResult.cs" />
    <Compile Include="Hub\Models\Project\ProjectResult.cs" />
    <Compile Include="Hub\Models\Project\ProjectSearchListResult.cs" />
    <Compile Include="Hub\Models\Project\ProjectShared.cs" />
    <Compile Include="Hub\ReportAvailabilityService.cs" />
    <Compile Include="Hub\Models\Reports\ClientBlendedReport.cs" />
    <Compile Include="Hub\Models\Reports\ProfileReportRequest.cs" />
    <Compile Include="Hub\Models\Reports\ReportRequest.cs" />
    <Compile Include="Hub\Models\Search\PagingResult.cs" />
    <Compile Include="Hub\Models\Search\PercentileFitFilter.cs" />
    <Compile Include="Hub\Models\Search\ProjectConfigResult.cs" />
    <Compile Include="Hub\Models\Search\SearchModel.cs" />
    <Compile Include="Hub\Models\AuthenticatedUserModel.cs" />
    <Compile Include="Hub\Models\Search\SearchResult.cs" />
    <Compile Include="Hub\Models\SuccessProfile\SuccessProfileUsageModel.cs" />
    <Compile Include="Hub\Models\SuccessProfile\SuccessProfileUsageResult.cs" />
    <Compile Include="Hub\MoreDetailsService.cs" />
    <Compile Include="Hub\ProjectAdditionalSPProjectService.cs" />
    <Compile Include="Hub\ProjectCreateUpdateService.cs" />
    <Compile Include="Hub\ProjectEmailService.cs" />
    <Compile Include="Hub\ProjectSearchService.cs" />
    <Compile Include="Hub\CandidateViewService.cs" />
    <Compile Include="Hub\SuccessProfileService.cs" />
    <Compile Include="Hub\ProjectViewService.cs" />
    <Compile Include="Hub\ReportService.cs" />
    <Compile Include="Hub\ServiceBase.cs" />
    <Compile Include="Hub\ServiceInterfaces\IAssessmentService.cs" />
    <Compile Include="Hub\ServiceInterfaces\ICandidateService.cs" />
    <Compile Include="Hub\ServiceInterfaces\IClientReportService.cs" />
    <Compile Include="Hub\ServiceInterfaces\IReportService.cs" />
    <Compile Include="Hub\Validators\CandidateCreateUpdateValidator.cs" />
    <Compile Include="Hub\Validators\CandidateReportRequestValidator.cs" />
    <Compile Include="Hub\Validators\EmailTemplateCreateUpdateValidator.cs" />
    <Compile Include="Hub\Validators\ProjectAdditionalSPsValidator.cs" />
    <Compile Include="Hub\Validators\SsoNotificationTypeValidator.cs" />
    <Compile Include="Hub\Validators\ProjectCreateUpdateValidator.cs" />
    <Compile Include="Hub\Validators\ProjectHubAssessmentValidator.cs" />
    <Compile Include="Hub\Validators\StatusUpdateModelValidator.cs" />
    <Compile Include="Hub\Validators\UniqueListValidator.cs" />
    <Compile Include="ICIMS\AssessmentResult.cs" />
    <Compile Include="ICIMS\iCimsDbRepository.cs" />
    <Compile Include="ICIMS\iCimsEventType.cs" />
    <Compile Include="ICIMS\iCimsService.cs" />
    <Compile Include="ICIMS\iCimsSettings.cs" />
    <Compile Include="ICIMS\IiCimsRepository.cs" />
    <Compile Include="ICIMS\NLogExtensions.cs" />
    <Compile Include="ICIMS\OperationFactory.cs" />
    <Compile Include="ICIMS\Order.cs" />
    <Compile Include="ICIMS\OrderOperationBase.cs" />
    <Compile Include="ICIMS\OrderOperationHub.cs" />
    <Compile Include="ICIMS\OrderOperationKfas.cs" />
    <Compile Include="ICIMS\OrderRequest.cs" />
    <Compile Include="ICIMS\Response.cs" />
    <Compile Include="ICIMS\Results\Kfas\DefaultResultsBuilder.cs" />
    <Compile Include="ICIMS\Results\Hub\FitResultBuilder.cs" />
    <Compile Include="ICIMS\Results\iCimsResultsHelpers.cs" />
    <Compile Include="ICIMS\Results\IResultsBuilder.cs" />
    <Compile Include="ICIMS\Results\ResultsBuilderFactory.cs" />
    <Compile Include="ICIMS\Results\Hub\SjtResultBuilder.cs" />
    <Compile Include="KFATS\Common\AssessmentStatusLegacyExtensions.cs" />
    <Compile Include="KFATS\Common\IntegrationStatus.cs" />
    <Compile Include="KFATS\Common\IOrderRepository.cs" />
    <Compile Include="KFATS\Common\Results\BandCalculatorConfiguration.cs" />
    <Compile Include="KFATS\Common\Results\Calculators\BandDefinition.cs" />
    <Compile Include="KFATS\Common\Results\Calculators\CalculatorScore.cs" />
    <Compile Include="KFATS\Common\Results\Calculators\DefaultBandCalculator.cs" />
    <Compile Include="KFATS\Common\Results\Calculators\DefaultPassCalculator.cs" />
    <Compile Include="KFATS\Common\Results\Calculators\IBandCalculator.cs" />
    <Compile Include="KFATS\Common\Results\Calculators\IPassCalculator.cs" />
    <Compile Include="KFATS\Common\Results\Calculators\IVerificationCalculator.cs" />
    <Compile Include="KFATS\Common\Results\Calculators\MacquarieVerificationCalculator.cs" />
    <Compile Include="KFATS\Common\Results\OverallResultConfiguration.cs" />
    <Compile Include="KFATS\Common\Results\OverallScoreType.cs" />
    <Compile Include="KFATS\Common\Results\PackageOverallResultBuilder.cs" />
    <Compile Include="KFATS\Common\Results\PassCalculatorConfiguration.cs" />
    <Compile Include="KFATS\Common\Results\ResultJsonConverter.cs" />
    <Compile Include="KFATS\Common\Results\ResultsHelper.cs" />
    <Compile Include="KFATS\Common\Results\UrlType.cs" />
    <Compile Include="KFATS\Common\Results\VerificationCalculatorConfiguration.cs" />
    <Compile Include="KFATS\Common\ServiceInterfaces\IOrderResultBuilder.cs" />
    <Compile Include="KFATS\Common\ServiceInterfaces\IOrderService.cs" />
    <Compile Include="KFATS\Common\ServiceInterfaces\IReportService.cs" />
    <Compile Include="KFATS\Common\KFATSException.cs" />
    <Compile Include="KFATS\Common\KfatsHandler.cs" />
    <Compile Include="KFATS\Common\Models\AssessmentStatusResponse.cs" />
    <Compile Include="KFATS\Common\Models\OrderRequest.cs" />
    <Compile Include="KFATS\Common\Models\OrderRequestCandidate.cs" />
    <Compile Include="KFATS\Common\Models\OrderRequestPackage.cs" />
    <Compile Include="KFATS\Common\Models\OrderResponse.cs" />
    <Compile Include="KFATS\Common\Models\OrderResponseCandidate.cs" />
    <Compile Include="KFATS\Common\Models\OrderResponsePackage.cs" />
    <Compile Include="KFATS\Common\Models\OrderResponsePackageAssessment.cs" />
    <Compile Include="KFATS\Common\Models\OrderStatusResponse.cs" />
    <Compile Include="KFATS\Common\Models\PackageStatusResponse.cs" />
    <Compile Include="KFATS\Common\Models\ResetRequest.cs" />
    <Compile Include="KFATS\Common\Models\Results\OrderPackageAssessmentDetailResult.cs" />
    <Compile Include="KFATS\Common\Models\Results\OrderPackageAssessmentResult.cs" />
    <Compile Include="KFATS\Common\Models\Results\OrderPackageResult.cs" />
    <Compile Include="KFATS\Common\Models\Results\OrderPackageURLResult.cs" />
    <Compile Include="KFATS\Common\Models\Results\OrderResult.cs" />
    <Compile Include="KFATS\Common\Models\Results\OverallResult.cs" />
    <Compile Include="KFATS\Common\Models\Results\Result.cs" />
    <Compile Include="KFATS\Common\Models\StatusResponse.cs" />
    <Compile Include="KFATS\Common\Models\TestingCompleteRequest.cs" />
    <Compile Include="KFATS\Common\NLogExtensions.cs" />
    <Compile Include="KFATS\Common\Order.cs" />
    <Compile Include="KFATS\Common\OrderDbRepository.cs" />
    <Compile Include="KFATS\Common\OrderPackageAssociation.cs" />
    <Compile Include="KFATS\Common\ServiceInterfaces\IResetService.cs" />
    <Compile Include="KFATS\Common\ServiceInterfaces\IStatusService.cs" />
    <Compile Include="KFATS\Common\StatusDetails.cs" />
    <Compile Include="KFATS\Common\StatusHelper.cs" />
    <Compile Include="KFATS\Hub\IKfatsHubRepository.cs" />
    <Compile Include="KFATS\Hub\KfatsHubDbRepository.cs" />
    <Compile Include="KFATS\Hub\OrderService.cs" />
    <Compile Include="KFATS\Hub\Package.cs" />
    <Compile Include="KFATS\Hub\ReportService.cs" />
    <Compile Include="KFATS\Hub\ResetService.cs" />
    <Compile Include="KFATS\Hub\Results\Builders\FitScoreBuilder.cs" />
    <Compile Include="KFATS\Hub\Results\Builders\IScoreBuilder.cs" />
    <Compile Include="KFATS\Hub\Results\Builders\KfasScoreItemExtensions.cs" />
    <Compile Include="KFATS\Hub\Results\Builders\SjtScoreBuilder.cs" />
    <Compile Include="KFATS\Hub\Results\Configuration\AssessmentResultBuilderConfiguration.cs" />
    <Compile Include="KFATS\Hub\Results\Configuration\PackageResultBuilderConfiguration.cs" />
    <Compile Include="KFATS\Hub\Results\Configuration\Report.cs" />
    <Compile Include="KFATS\Hub\Results\PackageURLResultBuilder.cs" />
    <Compile Include="KFATS\Hub\Results\OrderResultBuilder.cs" />
    <Compile Include="KFATS\Hub\Results\PackageResultBuilder.cs" />
    <Compile Include="KFATS\Hub\StatusService.cs" />
    <Compile Include="KFATS\Kfas\IKfasRepository.cs" />
    <Compile Include="KFATS\Kfas\KfasDbRepository.cs" />
    <Compile Include="KFATS\Kfas\OrderService.cs" />
    <Compile Include="KFATS\Kfas\Package.cs" />
    <Compile Include="KFATS\Kfas\ReportService.cs" />
    <Compile Include="KFATS\Kfas\ResetService.cs" />
    <Compile Include="KFATS\Kfas\Results\AspectsStylesScore.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\AbilityResultBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\AspectsAbilityResultsBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\AspectsCalculatorScoreBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\AspectsStylesResultBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\CompositeCalculatorScoreBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\CompositeResultBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\DimensionsResultBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\DrivesResultBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\ElementsResultBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\ICalculatorScoresBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\IResultBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\PlayerCalculatorScoreBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\PlayerTestResultBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\RoleProfileCalculatorScoreBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\SubTestResultBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Builders\VerificationCalculatorScoreBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\Configuration\PackageResultBuilderConfiguration.cs" />
    <Compile Include="KFATS\Kfas\Results\Configuration\ReportConfiguration.cs" />
    <Compile Include="KFATS\Kfas\Results\Configuration\ScoresBuilderConfiguration.cs" />
    <Compile Include="KFATS\Kfas\Results\IReportsHelper.cs" />
    <Compile Include="KFATS\Kfas\Results\IScoresService.cs" />
    <Compile Include="KFATS\Kfas\Results\OrderResultBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\PackageResultBuilder.cs" />
    <Compile Include="KFATS\Kfas\Results\ReportsHelper.cs" />
    <Compile Include="KFATS\Kfas\Results\Score.cs" />
    <Compile Include="KFATS\Kfas\Results\ScoresService.cs" />
    <Compile Include="KFATS\Kfas\Results\SubTestScore.cs" />
    <Compile Include="KFATS\Kfas\StatusService.cs" />
    <Compile Include="KFATS\KfatsQueryService.cs" />
    <Compile Include="KFATS\Common\KfatsUrlHelper.cs" />
    <Compile Include="KFATS\OrderController.cs" />
    <Compile Include="KFATS\ReportController.cs" />
    <Compile Include="KFATS\ResetController.cs" />
    <Compile Include="KFATS\ResultsController.cs" />
    <Compile Include="KFATS\ServiceFactory.cs" />
    <Compile Include="KFATS\StatusController.cs" />
    <Compile Include="KFATS\TestingController.cs" />
    <Compile Include="PageUp\Common\ClientSettings.cs" />
    <Compile Include="PageUp\Common\PageUpServiceFactoryBase.cs" />
    <Compile Include="PageUp\Hub\Results\FitScoreBuilder.cs" />
    <Compile Include="PageUp\Hub\Results\HubResultBuilder.cs" />
    <Compile Include="PageUp\Hub\Results\IScoreBuilder.cs" />
    <Compile Include="PageUp\Hub\Results\KfasScoreItemExtensions.cs" />
    <Compile Include="PageUp\Hub\Results\ReportResultBuilder.cs" />
    <Compile Include="PageUp\Hub\Results\SjtScoreBuilder.cs" />
    <Compile Include="PageUp\Kfas\Results\Client\MacquarieDimensionsTestResultBuilder.cs" />
    <Compile Include="PageUp\Kfas\Results\Client\MacquarieResultBuilder.cs" />
    <Compile Include="PageUp\Kfas\Results\ReferenceReportUrl.cs" />
    <Compile Include="PageUp\Kfas\Results\ReportResultBuilder.cs" />
    <Compile Include="PageUp\Results\IResultBuilder.cs" />
    <Compile Include="PageUp\Common\IPackage.cs" />
    <Compile Include="PageUp\Kfas\Results\KfasResultBuilder.cs" />
    <Compile Include="PageUp\Kfas\Results\AssessmentResultBuilder\AbilityTestResultBuilder.cs" />
    <Compile Include="PageUp\Kfas\Results\AssessmentResultBuilder\AspectsAbilityTestResultBuilder.cs" />
    <Compile Include="PageUp\Kfas\Results\AssessmentResultBuilder\AspectsStyleTestResultBuilder.cs" />
    <Compile Include="PageUp\Kfas\Results\AssessmentResultBuilder\DimensionsTestResultBuilder.cs" />
    <Compile Include="PageUp\Kfas\Results\AssessmentResultBuilder\DriversTestResultBuilder.cs" />
    <Compile Include="PageUp\Kfas\Results\AssessmentResultBuilder\ElementsTestResultBuilder.cs" />
    <Compile Include="PageUp\Kfas\Results\AssessmentResultBuilder\ElementsVerificationTestResultBuilder.cs" />
    <Compile Include="PageUp\Kfas\Results\AssessmentResultBuilder\PlayerTestResultBuilder.cs" />
    <Compile Include="PageUp\Common\PackageHelper.cs" />
    <Compile Include="PageUp\ResultsController.cs" />
    <Compile Include="PageUp\Common\ServicesFactoryResolver.cs" />
    <Compile Include="PageUp\Hub\HubOrderCandidateService.cs" />
    <Compile Include="PageUp\Hub\HubServicesFactory.cs" />
    <Compile Include="PageUp\Orders\IPackageOrder.cs" />
    <Compile Include="PageUp\Common\IPageUpRepository.cs" />
    <Compile Include="PageUp\Orders\IOrderCandidateService.cs" />
    <Compile Include="PageUp\Kfas\KfasServicesFactory.cs" />
    <Compile Include="PageUp\Kfas\OrderCandidateService.cs" />
    <Compile Include="PageUp\OrderController.cs" />
    <Compile Include="PageUp\Orders\PackageOrder.cs" />
    <Compile Include="PageUp\Common\IPageUpServicesFactory.cs" />
    <Compile Include="PageUp\Orders\PackageRetriver.cs" />
    <Compile Include="PageUp\Orders\RequestValidation.cs" />
    <Compile Include="PageUp\Orders\Models\ProcessCandidateResponse.cs" />
    <Compile Include="PageUp\Common\NLogExtensions.cs" />
    <Compile Include="PageUp\Common\Models\Order.cs" />
    <Compile Include="PageUp\Common\Models\Package.cs" />
    <Compile Include="PageUp\Results\PageUpAssessmentStatus.cs" />
    <Compile Include="PageUp\Common\PageUpDbRepository.cs" />
    <Compile Include="PageUp\Common\Models\PageUpException.cs" />
    <Compile Include="PageUp\PageUpService.cs" />
    <Compile Include="PageUp\Orders\Models\Request.cs" />
    <Compile Include="PageUp\Orders\Models\Response.cs" />
    <Compile Include="PageUp\Results\Models\AssessmentResult.cs" />
    <Compile Include="PageUp\Results\Models\ReportResult.cs" />
    <Compile Include="PageUp\Results\Models\Result.cs" />
    <Compile Include="PageUp\Results\Models\TestResult.cs" />
    <Compile Include="PageUp\Common\SecurityValidator.cs" />
    <Compile Include="PageUp\Orders\RequestSerializer.cs" />
    <Compile Include="PageUp\Orders\ResponseSerializer.cs" />
    <Compile Include="PageUp\Results\ResultsSerializer.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Hub\SanctionsConfirmationService.cs" />
    <Compile Include="Taleo\Allocators\BQJudgementAllocator.cs" />
    <Compile Include="Taleo\Allocators\DefaultAllocator.cs" />
    <Compile Include="Taleo\AssessmentExpiryService.cs" />
    <Compile Include="Taleo\CheckOperation.cs" />
    <Compile Include="Taleo\CheckRequest.cs" />
    <Compile Include="Taleo\ClientSettings.cs" />
    <Compile Include="Taleo\IAssessmentAllocator.cs" />
    <Compile Include="Taleo\IResultsBuilder.cs" />
    <Compile Include="Taleo\ITaleoRepository.cs" />
    <Compile Include="Taleo\NLogExtensions.cs" />
    <Compile Include="Taleo\Order.cs" />
    <Compile Include="Taleo\OrderAssessmentDetails.cs" />
    <Compile Include="Taleo\OrderDetails.cs" />
    <Compile Include="Taleo\OrderExtraData.cs" />
    <Compile Include="Taleo\OrderOperation.cs" />
    <Compile Include="Taleo\OrderRequest.cs" />
    <Compile Include="Taleo\OverallScore.cs" />
    <Compile Include="Taleo\Package.cs" />
    <Compile Include="Taleo\RequestBase.cs" />
    <Compile Include="Taleo\Response.cs" />
    <Compile Include="Taleo\Results\AbilityResultBand.cs" />
    <Compile Include="Taleo\Results\AbilityResultBuilder.cs" />
    <Compile Include="Taleo\Results\AssessmentScore.cs" />
    <Compile Include="Taleo\Results\BandDefinition.cs" />
    <Compile Include="Taleo\Results\BasicFitBandCalculator.cs" />
    <Compile Include="Taleo\Results\BasicFitPassCalculator.cs" />
    <Compile Include="Taleo\Results\BlendedResultsBuilder.cs" />
    <Compile Include="Taleo\Results\BQJudgementResultsBuilder.cs" />
    <Compile Include="Taleo\Results\CandidateResult.cs" />
    <Compile Include="Taleo\Results\CandidateResultBuilder.cs" />
    <Compile Include="Taleo\Results\CitiCompositeTestResultsBuilder.cs" />
    <Compile Include="Taleo\Results\CitiTestingResultsBuilder.cs" />
    <Compile Include="Taleo\Results\Clients\LibertyMutualBandCalculator.cs" />
    <Compile Include="Taleo\Results\Clients\ScbMultiPhaseResultsBuilder.cs" />
    <Compile Include="Taleo\Results\IBandCalculator.cs" />
    <Compile Include="Taleo\Results\Internal\AspectsScoresBuilder.cs" />
    <Compile Include="Taleo\Results\Internal\BlendedFitScoresBuilder.cs" />
    <Compile Include="Taleo\Results\Internal\DimensionsElementsScoresBuilder.cs" />
    <Compile Include="Taleo\Results\Internal\IBlendedStandardScoresBuilder.cs" />
    <Compile Include="Taleo\Results\Internal\IStandardScoresBuilder.cs" />
    <Compile Include="Taleo\Results\Internal\BlendedPlayerTestScoresBuilder.cs" />
    <Compile Include="Taleo\Results\Internal\PlayerTestScoresBuilder.cs" />
    <Compile Include="Taleo\Results\IPassCalculator.cs" />
    <Compile Include="Taleo\Results\OverallResult.cs" />
    <Compile Include="Taleo\Results\Result.cs" />
    <Compile Include="Taleo\Results\StandardResultBuilder.cs" />
    <Compile Include="Taleo\Security\RequestCredentials.cs" />
    <Compile Include="Taleo\Security\SecurityValidator.cs" />
    <Compile Include="Taleo\Serialization\AdditionalItems.cs" />
    <Compile Include="Taleo\Serialization\RequestSerializer.cs" />
    <Compile Include="Taleo\Serialization\ResponseSerializer.cs" />
    <Compile Include="Taleo\Serialization\ResultSerializer.cs" />
    <Compile Include="Taleo\TaleoAssessmentStatus.cs" />
    <Compile Include="Taleo\TaleoDbRepository.cs" />
    <Compile Include="Taleo\TaleoException.cs" />
    <Compile Include="Taleo\TaleoOperation.cs" />
    <Compile Include="Taleo\TaleoService.cs" />
    <Compile Include="Taleo\TaleoStatus.cs" />
    <Compile Include="V1\OperationBase.cs" />
    <Compile Include="V1\ReportOperation.cs" />
    <Compile Include="V2\Validation\CompetencyProfileAccessRule.cs" />
    <Compile Include="V2\NLogExtensions.cs" />
    <Compile Include="V2\OperationBase.cs" />
    <Compile Include="V2\ReportOperationBase.cs" />
    <Compile Include="V2\TalentQReportOperation.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\KornFerry.Admin\KornFerry.Admin.csproj">
      <Project>{F1791A90-D37E-44E8-A09A-4D983DDA9563}</Project>
      <Name>KornFerry.Admin</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Anonymisation\KornFerry.Anonymisation.csproj">
      <Project>{7a2d4ad5-22d7-4da8-98d5-b9c40e0d8c54}</Project>
      <Name>KornFerry.Anonymisation</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Assessments\KornFerry.Assessments.csproj">
      <Project>{4558e7ee-09b7-48c9-b323-c29fa095c2cd}</Project>
      <Name>KornFerry.Assessments</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Authentication\KornFerry.Authentication.csproj">
      <Project>{9256BF18-094E-49A7-B961-2344BBA526EF}</Project>
      <Name>KornFerry.Authentication</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.AWS\KornFerry.AWS.csproj">
      <Project>{EE897FF6-386B-4950-BAA3-C8248B7C2900}</Project>
      <Name>KornFerry.AWS</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Branding\KornFerry.Branding.csproj">
      <Project>{5EFB9418-7055-4D03-BDB3-474E3B3AB50F}</Project>
      <Name>KornFerry.Branding</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Caching\KornFerry.Caching.csproj">
      <Project>{aeb4ff99-4579-4333-b755-c984c44d1e44}</Project>
      <Name>KornFerry.Caching</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.ClientNotification\KornFerry.ClientNotification.csproj">
      <Project>{2F47C277-0655-4F43-BCFC-EF10317890F3}</Project>
      <Name>KornFerry.ClientNotification</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Cms\KornFerry.Cms.csproj">
      <Project>{568dee09-0cf8-4bb4-bdf3-cc89e15e754d}</Project>
      <Name>KornFerry.Cms</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services.Assessments\KornFerry.Services.Assessments.csproj">
      <Project>{7DE64529-99AD-45C6-A144-16AD1EDC525C}</Project>
      <Name>KornFerry.Services.Assessments</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services.BatchUploads\KornFerry.Services.BatchUploads.csproj">
      <Project>{f57684f6-1209-4a66-90c5-06eb35c676d0}</Project>
      <Name>KornFerry.Services.BatchUploads</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services.BulkPhotoUploads\KornFerry.Services.BulkPhotoUploads.csproj">
      <Project>{CC0B60F5-95D3-4456-AB84-87A9733ABFA7}</Project>
      <Name>KornFerry.Services.BulkPhotoUploads</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services.Commercial\KornFerry.Services.Commercial.csproj">
      <Project>{72046f34-6fa2-4bd1-8208-5eebb9ee2fd1}</Project>
      <Name>KornFerry.Services.Commercial</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services.Content\KornFerry.Services.Content.csproj">
      <Project>{582354a6-1f8c-4784-b0e0-403e47eb0215}</Project>
      <Name>KornFerry.Services.Content</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services.Extracts\KornFerry.Services.Extracts.csproj">
      <Project>{AFBE776F-6B3B-4297-86F0-48B16946AB07}</Project>
      <Name>KornFerry.Services.Extracts</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services.KFLearn\KornFerry.Services.KFLearn.csproj">
      <Project>{84CC3D95-206D-49BB-8A8A-7D623AAC78A3}</Project>
      <Name>KornFerry.Services.KFLearn</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services.Reflex\KornFerry.Services.Reflex.csproj">
      <Project>{75764240-F0D5-4151-B1C5-760405C628CC}</Project>
      <Name>KornFerry.Services.Reflex</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services.Reporting.Configurable\KornFerry.Services.Reporting.Configurable.csproj">
      <Project>{E1367877-5879-4EB2-9BA8-CCEC7A4F401F}</Project>
      <Name>KornFerry.Services.Reporting.Configurable</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services.Reporting\KornFerry.Services.Reporting.csproj">
      <Project>{BF02356D-7E03-42C4-94E3-D2E91886E761}</Project>
      <Name>KornFerry.Services.Reporting</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services.Scoring\KornFerry.Services.Scoring.csproj">
      <Project>{0DB5381C-C553-4CBE-8B1F-7E77E3F9C024}</Project>
      <Name>KornFerry.Services.Scoring</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.Services\KornFerry.Services.csproj">
      <Project>{df781d72-3d09-4a3f-ba15-ee2abb9cd564}</Project>
      <Name>KornFerry.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\KornFerry.WebDocumentation\KornFerry.WebDocumentation.csproj">
      <Project>{7EF200D0-9FCB-4E01-8841-9ED911E41F0B}</Project>
      <Name>KornFerry.WebDocumentation</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.AssessmentAutomation\TalentQ.AssessmentAutomation.csproj">
      <Project>{72be7da1-d444-482e-9fe7-ea50e4e2c74e}</Project>
      <Name>TalentQ.AssessmentAutomation</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.Assessments\TalentQ.Assessments.csproj">
      <Project>{78825721-ec6a-4c50-a5cf-56c58f4f9e34}</Project>
      <Name>TalentQ.Assessments</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.Core\TalentQ.Core.csproj">
      <Project>{C5242461-DB97-4878-9D40-F37F2737342C}</Project>
      <Name>TalentQ.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.Data\TalentQ.Data.csproj">
      <Project>{ffc0d520-ee90-46e0-9ab7-084d43cb6852}</Project>
      <Name>TalentQ.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.Reporting.Custom\TalentQ.Reporting.Custom.csproj">
      <Project>{9a378696-fec2-4bcf-9550-bbb3cd9d722c}</Project>
      <Name>TalentQ.Reporting.Custom</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.Reporting.Pdf\TalentQ.Reporting.Pdf.csproj">
      <Project>{81f40639-6570-45b5-b4b8-72ecfe0565c6}</Project>
      <Name>TalentQ.Reporting.Pdf</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.Reporting.Standard\TalentQ.Reporting.Standard.csproj">
      <Project>{5cc60187-70ea-4beb-ade0-a62f2847e236}</Project>
      <Name>TalentQ.Reporting.Standard</Name>
    </ProjectReference>
    <ProjectReference Include="..\TalentQ.Utilities\TalentQ.Utilities.csproj">
      <Project>{3FF20D05-08C2-40DB-A419-510BAA064D35}</Project>
      <Name>TalentQ.Utilities</Name>
    </ProjectReference>
    <ProjectReference Include="..\TQCommon\TQCommon.csproj">
      <Project>{E473FCAA-4E5F-4AED-A7FC-3AD6784BA49A}</Project>
      <Name>TQCommon</Name>
    </ProjectReference>
    <ProjectReference Include="..\TQData\TQData.csproj">
      <Project>{9b94bfd8-1030-4e0a-a29d-4526a459a4d1}</Project>
      <Name>TQData</Name>
    </ProjectReference>
    <ProjectReference Include="..\TQIntShared\TQIntShared.csproj">
      <Project>{94710873-327c-441a-b6e5-1f3b91601a2e}</Project>
      <Name>TQIntShared</Name>
    </ProjectReference>
    <ProjectReference Include="..\TQLogic\TQLogic.csproj">
      <Project>{1398EA65-2390-477E-8ACA-0AF976F077FB}</Project>
      <Name>TQLogic</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\packages\StyleCop.Analyzers.1.0.0\analyzers\dotnet\cs\Newtonsoft.Json.dll" />
    <Analyzer Include="..\packages\StyleCop.Analyzers.1.0.0\analyzers\dotnet\cs\StyleCop.Analyzers.CodeFixes.dll" />
    <Analyzer Include="..\packages\StyleCop.Analyzers.1.0.0\analyzers\dotnet\cs\StyleCop.Analyzers.dll" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>