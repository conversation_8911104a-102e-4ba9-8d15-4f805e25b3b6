﻿using KornFerry.Services.Scoring.Shared.Storage;
using KornFerry.Services.Scoring.Shared.Storage.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using TalentQ.Core.Users.BioData;
using TalentQ.Data.SqlServer;
using TQCommon;
using TQData.Utilities;
using TQLogic.Assessments;
using TQLogic.Proctoring;
using TQLogic.Projects;
using TQLogic.Projects.Hub;
using TQLogic.Search.Administration;
using ExtractScoreKey = KornFerry.Services.Extracts.Service.Models.ExtractScoreKey;

namespace KornFerry.Services.Extracts.Service
{
    /// <summary>
    /// Score database retriever.
    /// </summary>
    /// <seealso cref="KornFerry.Services.Extracts.Service.IScoreExtract" />
    public class ScoreDbExtract : IScoreExtract
    {
        /// <summary>
        /// Gets the scores for candidates in a project.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// Candidates, and multiple project scores
        /// </returns>
        public async Task<CandidateExtractResult> GetScoresAsync(ExtractRequest request)
        {
            return await HubExtracts_SearchCandidatesAsync(request, true, r =>
            {
                var result = MapResults(r);
                result.Request = request;
                return result;
            }).ConfigureAwait(false);
        }

        public async Task<IEnumerable<int>> GetCandidateIdsAsync(ExtractRequest request)
        {
            return await HubExtracts_SearchCandidatesAsync(request, false, r => r.ToObjects(reader => reader.GetInt32("Id")));
        }

        private async Task<T> HubExtracts_SearchCandidatesAsync<T>(ExtractRequest request, bool returnScores, Func<SqlDataReader, T> readerAction)
        {
            using (var procedure = new SqlStoredProcedure("HubExtracts_SearchCandidate"))
            {
                procedure
                    .AddInParameter("@searchString", request.SearchTerm, 256)
                    .AddStructuredParameter("@projectIds", "IdTableType", TQDataUtility.ConvertIdsToRecords(request.ProjectIds))
                    .AddInParameter("@returnScores", returnScores)
                    .AddInParameter("@includeNonBillable", request.IncludeNonBillable)
                    .AddInParameter("@includeAnonymisedCandidates", request.IncludeAnonymisedCandidates);

                if (request.Fits.Any())
                {
                    procedure.AddStructuredParameter("@fits", "IdTableType", TQDataUtility.ConvertIdsToRecords(request.Fits));
                }

                if (request.ScoreTypes.Any())
                {
                    procedure.AddStructuredParameter("@scoreTypes", "IdTableType", TQDataUtility.ConvertIdsToRecords(request.ScoreTypes.Select(s => (int)s)));
                }

                if (request.Statuses.Any())
                {
                    procedure.AddStructuredParameter("@status", "IdTableType", TQDataUtility.ConvertIdsToRecords(request.Statuses.Select(s => (int)s)));
                }

                if (request.CandidateIds.Any())
                {
                    procedure.AddStructuredParameter("@candidateIds", "IdTableType", TQDataUtility.ConvertIdsToRecords(request.CandidateIds));
                }

                if (request.TargetLevels.Any())
                {
                    procedure.AddStructuredParameter("@targetLevels", "StringTableType", TQDataUtility.ConvertStringsToRecords(request.TargetLevels));
                }

                if (request.FitPercentiles?.Any() ?? false)
                {
                    procedure.AddStructuredParameter("@fitPercentiles", "IdTableType", TQDataUtility.ConvertIdsToRecords(request.FitPercentiles));
                }

                if (request.FitRange != null)
                {
                    procedure.AddStructuredParameter("@fitRange", "DecimalRange", TQDataUtility.ConvertDecimalRangeToRecords(new Tuple<decimal, decimal>(request.FitRange.Item1, request.FitRange.Item2)));
                }

                if (request.FitDecimalRange != null)
                {
                    procedure.AddStructuredParameter("@fitDecimalRange", "DecimalRange", TQDataUtility.ConvertDecimalRangeToRecords(request.FitDecimalRange));
                }

                if (request.FitPercentileRange != null)
                {
                    procedure.AddStructuredParameter("@fitPercentileRange", "DecimalRange", TQDataUtility.ConvertDecimalRangeToRecords(new Tuple<decimal, decimal>(request.FitPercentileRange.Item1, request.FitPercentileRange.Item2)));
                }

                return readerAction(await procedure.ExecuteReaderAsync().ConfigureAwait(false));
            }
        }

        /// <summary>
        /// Gets the scores for particular candidates.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// Candidates, and multiple project scores
        /// </returns>
        public CandidateExtractResult GetScores(ExtractScoresRequest request)
        {
            return GetScoresAsync(request, false).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Gets the scores for particular candidates.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// Candidates, and multiple project scores
        /// </returns>
        public Task<CandidateExtractResult> GetScoresAsync(ExtractScoresRequest request)
        {
            return GetScoresAsync(request, true);
        }

        /// <summary>
        /// Gets the scores for particular candidates.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// Candidates, and multiple project scores
        /// </returns>
        private async Task<CandidateExtractResult> GetScoresAsync(ExtractScoresRequest request, bool async)
        {
            using (var procedure = new SqlStoredProcedure("HubExtracts_GetCandidateScores"))
            {
                procedure
                    .AddStructuredParameter("@candidateIds", "IdTableType", TQDataUtility.ConvertIdsToRecords(request.CandidateIds))
                    .AddStructuredParameter("@projectIds", "IdTableType", TQDataUtility.ConvertIdsToRecords(request.ProjectIds))
                    .AddStructuredParameter("@scoreTypes", "IdTableType", TQDataUtility.ConvertIdsToRecords(request.ScoreTypes.Select(s => (int)s)));

                if (request.TargetLevels.Any())
                {
                    procedure.AddStructuredParameter("@targetLevels", "StringTableType", TQDataUtility.ConvertStringsToRecords(request.TargetLevels));
                }

                var reader = async ? await procedure.ExecuteReaderAsync().ConfigureAwait(false)
                                   : procedure.ExecuteReader();

                return MapResults(reader);
            }
        }

        /// <summary>
        /// Populates the demographics, details for an already populated result set.
        /// </summary>
        /// <param name="result">The result set to add the demographics to.</param>
        public async Task PopulateDemographicsAsync(CandidateExtractResult result)
        {
            Check.IsTrue(result.Request.ProjectIds.Count() == 1, "Demographics can only be extracted for a single ProjectId");

            using (var procedure = new SqlStoredProcedure("HubExtracts_GetCandidateDemographics"))
            {
                var reader = procedure
                    .AddInParameter("@projectId", result.Request.ProjectIds.First())
                    .AddStructuredParameter("@candidateIds", "IdTableType", TQDataUtility.ConvertIdsToRecords(result.Candidates.Select(c => c.Candidate.CandidateId)))
                    .ExecuteReaderAsync().ConfigureAwait(false);

                (await reader).ForEachRow(MapDemographics(result));
            }
        }

        private Action<SqlDataReaderAdapter> MapDemographics(CandidateExtractResult result)
        {
            return reader =>
            {
                var candidateId = reader.GetInt32("CandidateId");
                var key = reader.GetString("Key");

                var candidateResult = result.Candidates.FirstOrDefault(c => c.Candidate.CandidateId == candidateId);

                candidateResult.Candidate.Demographics.Add(key, new BioDataResponseItem
                {
                    Id = reader.GetInt32("BioDataResponseItemId"),
                    Key = key,
                    Type = reader.GetEnum("Type", TalentQ.Core.Users.BioData.ValueType.Single),
                    Value = reader.GetStringOrNull("Value")
                });
            };
        }

        private CandidateExtractResult MapResults(SqlDataReader reader)
        {
            // Lookups to help speed up the searching of existing records.
            Dictionary<int, ScoreResult> scoresCache = new Dictionary<int, ScoreResult>(); // Key is the ScoreId unique across the system.

            var result = new CandidateExtractResult();

            reader
                .ToObjectsThenNextResult(result.Candidates, MapCandidate())
                .ForEachRowThenNextResult(MapProjects(result))
                .ForEachRowThenNextResult(MapAssessments(result))
                .ForEachRowThenNextResult(MapScoreHeaders(result, scoresCache))
                .ForEachRow(MapScoreValues(scoresCache));

            return result;
        }

        private Func<SqlDataReaderAdapter, CandidateResult> MapCandidate()
        {
            return reader =>
            {
                return new CandidateResult()
                {
                    Candidate = new CandidateInfo
                    {
                        CandidateId = reader.GetInt32("CandidateId"),
                        ExternalClientId = HubClientReference.ExternalClientId(reader.GetString("ExternalClientId")),
                        UserGroup = HubClientReference.UserGroup(reader.GetString("ExternalClientId")),
                        Firstname = reader.GetString("Forename"),
                        Lastname = reader.GetString("Surname"),
                        Email = reader.GetStringOrEmpty("Email"),
                        DateCreated = reader.GetDateTime("DateCreated"),
                        ExEmployeeId = reader.GetStringOrNull("ExEmployeeId")
                    }
                };
            };
        }

        private Action<SqlDataReaderAdapter> MapProjects(CandidateExtractResult result)
        {
            return reader =>
            {
                var candidateId = reader.GetInt32("CandidateId");
                var projectId = reader.GetInt32("ProjectId");

                var candidateResult = result.Candidates.FirstOrDefault(c => c.Candidate.CandidateId == candidateId);

                var project = candidateResult.Projects.FirstOrDefault(p => p.ProjectId == projectId);
                if (project == null)
                {
                    project = new ProjectResult
                    {
                        ProjectId = projectId,
                        Status = reader.GetEnum<ProjectCandidateStatus>("Status", 0),
                        LastActivity = reader.GetDateTime("LastActivity"),
                        Completed = reader.GetDateTimeNullable("Completed"),
                        Hired = (HubCandidateHiredStatus)reader.GetInt32("Hired"),
                        ProctoringOverallScore = reader.GetEnumNullable<CandidateProctoringScore>("ProctoringOverallScore"),
                    };

                    candidateResult.Projects.Add(project);
                }
            };
        }

        private Action<SqlDataReaderAdapter> MapAssessments(CandidateExtractResult result)
        {
            return reader =>
            {
                var candidateId = reader.GetInt32("CandidateId");
                var candidateResult = result.Candidates.FirstOrDefault(c => c.Candidate.CandidateId == candidateId);

                candidateResult.Candidate.Assessments.Add(new AssessmentOverview
                {
                    Type = reader.GetEnum("TestID", AssessmentType.None).ToBlendedAssessmentType(),
                    Status = reader.GetEnum("InProgress", AssessmentStatusLegacy.NotStarted),
                    CompletedDate = reader.GetDateTimeNullable("Completed"),
                    LanguageId = reader.GetInt32("LanguageId"),
                    ProjectId = reader.GetInt32("ProjectId")
                });
            };
        }

        private Action<SqlDataReaderAdapter> MapScoreHeaders(CandidateExtractResult result, Dictionary<int, ScoreResult> scoresCache)
        {
            return reader =>
            {
                var candidateId = reader.GetInt32("CandidateId");
                var scoreId = reader.GetInt32("ScoreId");
                var projectId = reader.GetInt32("ProjectId");
                var scoreType = reader.GetEnum<ScoreType>("Type", 0);
                var testType = reader.GetEnumNullable<AssessmentType>("TestId")?.ToBlendedAssessmentType() ?? BlendedAssessmentType.None;
                var targetLevel = reader.GetStringOrNull("TargetLevel");

                var candidateResult = result.Candidates.First(c => c.Candidate.CandidateId == candidateId);

                var project = candidateResult.Projects.FirstOrDefault(p => p.ProjectId == projectId);
                if (project == null)
                {
                    project = new ProjectResult
                    {
                        ProjectId = projectId
                    };

                    candidateResult.Projects.Add(project);
                }

                if (!scoresCache.ContainsKey(scoreId))
                {
                    var scoreResult = new ScoreResult();
                    project.Scores.Add(new ScoreDetails { Type = scoreType, Assessment = testType, TargetLevel = targetLevel }, scoreResult);

                    scoresCache.Add(scoreId, scoreResult);
                }
            };
        }

        private Action<SqlDataReaderAdapter> MapScoreValues(Dictionary<int, ScoreResult> scoresCache)
        {
            return reader =>
            {
                var scoreId = reader.GetInt32("KFASScoreId");

                scoresCache[scoreId].Add(new KfasScoreItem
                {
                    ItemId = reader.GetInt32("KFASScoreItemId"),
                    ParentScoreId = scoreId,
                    Group = reader.GetEnum("Group", ScoreGroup.Overall),
                    Key = reader.GetString("Key"),
                    RawScore = (double?)reader.GetDecimalNullable("RawScore"),
                    ZScore = (double?)reader.GetDecimalNullable("ZScore"),
                    TScore = reader.GetInt32Nullable("TScore"),
                    Percentile = reader.GetInt32Nullable("Percentile"),
                    PercentileAbsolute = (double?)reader.GetDecimalNullable("PercentileAbsolute"),
                    Sten = reader.GetInt32Nullable("Sten"),
                    StenAbsolute = (double?)reader.GetDecimalNullable("StenAbsolute"),
                    HalfSten = reader.GetInt32Nullable("HalfSten"),
                    Fit = reader.GetInt32Nullable("Fit"),
                    FitAbsolute = (double?)reader.GetDecimalNullable("FitAbsolute"),
                    Lower = (double?)reader.GetDecimalNullable("Lower"),
                    Upper = (double?)reader.GetDecimalNullable("Upper")
                });
            };
        }

        public async Task<IEnumerable<ExtractScoreKey>> GetMissingScoreKeysAsync(ExtractScoresRequest request)
        {
            var dbResults = new List<ExtractScoreKey>();
            using (var procedure = new SqlStoredProcedure("HubExtracts_GetCandidateScoreHeaders"))
            {
                procedure
                    .AddStructuredParameter("@candidateIds", "IdTableType", TQDataUtility.ConvertIdsToRecords(request.CandidateIds))
                    .AddStructuredParameter("@projectIds", "IdTableType", TQDataUtility.ConvertIdsToRecords(request.ProjectIds))
                    .AddStructuredParameter("@scoreTypes", "IdTableType", TQDataUtility.ConvertIdsToRecords(request.ScoreTypes.Select(s => (int)s)));

                if (request.TargetLevels.Any())
                {
                    procedure.AddStructuredParameter("@targetLevels", "StringTableType", TQDataUtility.ConvertStringsToRecords(request.TargetLevels));
                }

                (await procedure.ExecuteReaderAsync().ConfigureAwait(false)).ToObjects(dbResults, r => new ExtractScoreKey(r.GetInt32("CandidateId"), r.GetInt32("ProjectId"), r.GetEnum<ScoreType>("Type", 0), r.GetStringOrNull("ContextTargetLevel")));
            }

            var expectedResults = request.AsScoreKeys;
            return from e in expectedResults
                   join d in dbResults on e equals d into db
                   from d in db.DefaultIfEmpty()
                   where d == null
                   select e;
        }
    }
}
