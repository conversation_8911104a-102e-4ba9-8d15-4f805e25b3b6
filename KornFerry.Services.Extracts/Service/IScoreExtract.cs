﻿using KornFerry.Services.Extracts.Service.Models;
using KornFerry.Services.Reflex.Models.Responses.TechnicalSkills;

using System.Collections.Generic;
using System.Threading.Tasks;

namespace KornFerry.Services.Extracts.Service
{
    /// <summary>
    /// Retrieve candidate scores for data extracts.
    /// </summary>
    public interface IScoreExtract
    {
        /// <summary>
        /// Gets the scores for candidates in a project.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Candidates, and multiple project scores</returns>
        Task<CandidateExtractResult> GetScoresAsync(ExtractRequest request);

        /// <summary>
        /// Gets the scores for particular candidates.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Candidates, and multiple project scores</returns>
        CandidateExtractResult GetScores(ExtractScoresRequest request);

        /// <summary>
        /// Gets the scores for particular candidates.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Candidates, and multiple project scores</returns>
        Task<CandidateExtractResult> GetScoresAsync(ExtractScoresRequest request);

        /// <summary>
        /// Gets a list of candidate Ids that meet the filter criteria within the <see cref="request"/>
        /// </summary>
        /// <param name="request">The request containing the filter criteria</param>
        /// <returns>List of Candidate Ids</returns>
        Task<IEnumerable<int>> GetCandidateIdsAsync(ExtractRequest request);

        /// <summary>
        /// Populates the demographics, details for an already populated result set.
        /// </summary>
        /// <param name="result">The result set to add the demographics to.</param>
        Task PopulateDemographicsAsync(CandidateExtractResult result);

        /// <summary>
        /// Gets the scores that do not exist in the repository based on the filter criteria within a <paramref name="request"/>
        /// </summary>
        /// <param name="request">The request</param>
        /// <returns>One result per candidate within the <paramref name="request"/></returns>
        Task<IEnumerable<ExtractScoreKey>> GetMissingScoreKeysAsync(ExtractScoresRequest request);
    }
}