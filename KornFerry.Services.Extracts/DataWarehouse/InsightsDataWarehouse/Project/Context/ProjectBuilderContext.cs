﻿using System;
using TQLogic.Projects.Hub;
using TQLogic.Projects;
using TQLogic;
using TalentQ.Utilities;
using System.Collections.Generic;
using KornFerry.Services.Extracts.Blended.Extracts.PotentialTalentGrid.LearningAgilitySection;

namespace KornFerry.Services.Extracts.DataWarehouse.InsightsDataWarehouse.Project.Context
{
    /// <summary>
    /// A context for building the project data extract
    /// </summary>
    public class ProjectBuilderContext : IProjectBuilderContext
    {
        /// <summary>
        /// Creates a new <see cref="ProjectBuilderContext"/>
        /// </summary>
        /// <param name="project">The project to build the context for</param>
        /// <param name="language">The language to use for any translations</param>
        public ProjectBuilderContext(ProjectHub project, Language language)
        {
            Check.NotNull(project, "Must provide a project");
            Check.NotNull(language, "Must provide a language");

            var client = !string.IsNullOrEmpty(project.UserGroup)
                && project.Project.ClientOwner.Name.Equals(HubClientReference.UserGroupClientName(project.UserGroup)) ?
                project.Project.ClientOwner.ParentClient
                : project.Project.ClientOwner;

            // Most of the main properties for an additional success profile project are defined on the original project.
            var mainProject = project.ParentProjectHub ?? project;

            Client = new ProjectBuilderContextClient
            {
                KfasClientId = project.Project.ClientOwnerId,
                ExternalClientId = project.ExternalClientId.Value,
                Name = client.Name,
                UserGroupId = project.UserGroup
            };

            Project = new ProjectBuilderContextProject
            {
                ProjectId = mainProject.ProjectId.Value,
                ProjectName = mainProject.Project.Name,
                ProjectType = mainProject.ProjectType,
                CustomProjectTypeId = mainProject.CustomProjectTypeId,
                CustomProjectTypeName = mainProject.CustomProjectTypeName,
                ProductType = mainProject.ProductType,
                CreatedDate = mainProject.Project.Created,
                CreatedByJson = mainProject.CreatedByJson,
                EndDate = mainProject.Project.Deadline,
                ServiceLevel = mainProject.Commercial?.ServiceType,
                CostCenterEngagementCode = mainProject.Commercial?.EngagementNo,
                SystemPlatform = mainProject.Platform,
                IsIntegration = mainProject.Project.IsIntegrationProject(),
                IsNonBillable = mainProject.IsNonBillable,
                AssessmentReuse = mainProject.AllowAssessmentReuse,
                HasPotentialLevels = mainProject.HasPotentialLevels,
                IncludeDerailers = mainProject.IncludeDerailers ?? false,
                LearningAgilityAddOn = mainProject.IncludeLearningAgility,
                PotentialCurrentLevel = mainProject.CurrentLevel,
                PotentialTargetLevel = mainProject.TargetLevel,
                Location = mainProject.Location,
                Assessments = project.Assessments // Additional success profiles can have a different list of assessments.
            };

            if (project.SuccessProfileId.HasValue)
            {
                SuccessProfile = new ProjectBuilderContextSuccessProfile
                {
                    SuccessProfileId = project.SuccessProfileId.Value,
                    SuccessProfilePublished = project.IsSuccessProfilePublished,
                    IsPrimarySuccessProfile = !project.IsChildProject,
                    IsCustomised = project.SuccessProfileCustomized ?? false,
                    ScoresJson = project.BicScoresJson,
                    Norm = project.Norm ?? ProjectHubNorm.Default,
                    CultureSort = project.Assessments?.Drivers?.Data,
                    JobCharacteristicData = project.Assessments?.Traits?.Data,
                    Targets = project.Targets
                };
            }

            Language = language;
            Validate();
        }

        /// <summary>
        /// Validates this instance
        /// </summary>
        private void Validate()
        {
            var validationResult = new ProjectBuilderContextValidator().Validate(this);
            if (!validationResult.IsValid)
            {
                throw new Exception($"Error validating ProjectBuilderContext. Errors: {validationResult.Errors}");
            }
        }

        /// <summary>
        /// Client details
        /// </summary>
        public ProjectBuilderContextClient Client { get; internal set; }

        /// <summary>
        /// Project details
        /// </summary>
        public ProjectBuilderContextProject Project { get; internal set; }

        /// <summary>
        /// Success profile
        /// </summary>
        public ProjectBuilderContextSuccessProfile SuccessProfile { get; internal set; }

        /// <summary>
        /// Content language for any translations
        /// </summary>
        public Language Language { get; internal set; }
    }

    /// <summary>
    /// Client context
    /// </summary>
    public class ProjectBuilderContextClient
    {
        /// <summary>
        /// The Kfas client Id
        /// </summary>
        public int KfasClientId { get; internal set; }

        /// <summary>
        /// The external client Id
        /// </summary>
        public int ExternalClientId { get; internal set; }

        /// <summary>
        /// Name of the client
        /// </summary>
        public string Name { get; internal set; }

        /// <summary>
        /// Client user group Id
        /// </summary>
        public string UserGroupId { get; internal set; }
    }

    /// <summary>
    /// Project context
    /// </summary>
    public class ProjectBuilderContextProject
    {
        /// <summary>
        /// The project Id
        /// </summary>
        public int ProjectId { get; internal set; }

        /// <summary>
        /// Name of the project
        /// </summary>
        public string ProjectName { get; internal set; }

        /// <summary>
        /// The date the project was created.
        /// </summary>
        public DateTime CreatedDate { get; internal set; }

        /// <summary>
        /// The details of the admin who created the project.
        /// </summary>
        public string CreatedByJson { get; internal set; }

        /// <summary>
        /// Optional date the project will end
        /// </summary>
        public DateTime? EndDate { get; internal set; }

        /// <summary>
        /// The type of Products Hub project
        /// </summary>
        public ProjectHubType ProjectType { get; internal set; }

        /// <summary>
        /// The id of the custom project type, but only if <see cref="ProjectType"/> is <see cref="ProjectHubType.Custom"/>
        /// </summary>
        public int? CustomProjectTypeId { get; internal set; }

        /// <summary>
        /// The custom project type Name, Optional, used in case of <see cref="ProjectType"/> is <see cref="ProjectHubType.Custom"/>
        /// </summary>
        public string CustomProjectTypeName { get; internal set; }

        /// <summary>
        /// The product type
        /// </summary>
        public HubProductType ProductType { get; internal set; }

        /// <summary>
        /// Assessments used in this project
        /// </summary>
        public ProjectHubAssessments Assessments { get; internal set; }

        /// <summary>
        /// Project location data
        /// </summary>
        public Location Location { get; internal set; }

        /// <summary>
        /// The default current potential level assigned to candidates when they are added to the project, 
        /// or null if potential isn't being used by this project.
        /// </summary>
        public string PotentialCurrentLevel { get; internal set; }

        /// <summary>
        /// The default target potential level assigned to candidates when they are added to the project, 
        /// and the level used to score and generate reports against,
        /// or null if potential isn't being used by this project.
        /// </summary>
        public string PotentialTargetLevel { get; internal set; }

        /// <summary>
        /// Who manages the project, either KF or the client.
        /// </summary>
        public ProjectHubServiceType? ServiceLevel { get; internal set; }

        /// <summary>
        /// The clients cost center to bill the project to.
        /// </summary>
        public string CostCenterEngagementCode { get; internal set; }

        /// <summary>
        /// The third party KF system interfacing with KFAS, e.g. KF Nimble or KF Advance
        /// </summary>
        public string SystemPlatform { get; internal set; }

        /// <summary>
        /// Is this project used by an integration
        /// </summary>
        public bool IsIntegration { get; internal set; }

        /// <summary>
        /// Is the project a test project and the client shouldn't be charged.
        /// True, if the project shouldn't be charged.
        /// False, if the project should be charged.
        /// </summary>
        public bool IsNonBillable { get; internal set; }

        /// <summary>
        /// Are candidates allowed to re-use their previously completed assessment responses for new projects.
        /// </summary>
        public bool AssessmentReuse { get; internal set; }

        /// <summary>
        /// Does the project have any potential levels, I.e. use the potential norms, scoring and reports.
        /// </summary>
        public bool HasPotentialLevels { get; internal set; }

        /// <summary>
        /// Has derailers been enabled for the project and should be included in the reports.
        /// </summary>
        public bool IncludeDerailers { get; internal set; }

        /// <summary>
        /// Has the learning agility add-on been enabled for the project.
        /// </summary>
        public bool LearningAgilityAddOn { get; internal set; }
    }

    /// <summary>
    /// Success profile context
    /// </summary>
    public class ProjectBuilderContextSuccessProfile
    {
        /// <summary>
        /// External Id of the Success Profile. Usage depends on <see cref="ProjectType"/>
        /// </summary>
        public int SuccessProfileId { get; internal set; }

        /// <summary>
        /// If the admin decided to customize the success profile via the SPS system then the success profile hasn't been published.
        /// Which means the report items, assessments, scores etc aren't completely defined yet.
        /// </summary>
        public bool SuccessProfilePublished { get; internal set; }

        /// <summary>
        /// Was the success profile customized by the admin when creating the project.
        /// </summary>
        public bool IsCustomised { get; internal set; }

        /// <summary>
        /// True, when the success profile is the original one used to create the project.
        /// False, when this relates to an additional success profile added to a project.
        /// </summary>
        public bool IsPrimarySuccessProfile { get; internal set; }

        /// <summary>
        /// Scores json, including the norms and scores for the success profile.
        /// </summary>
        public string ScoresJson { get; internal set; }

        /// <summary>
        /// The norm details for the project.
        /// </summary>
        public ProjectHubNorm Norm { get; internal set; }

        /// <summary>
        /// The company culture sort order
        /// </summary>
        public List<string> CultureSort { get; internal set; }

        /// <summary>
        /// The slider scores for the job characteristics.
        /// </summary>
        public List<Values> JobCharacteristicData { get; internal set; }

        /// <summary>
        /// The project targets, if relevant to the type of project
        /// </summary>
        public ProjectHubTargets Targets { get; internal set; }
    }

    public interface IProjectBuilderContext
    {
        /// <summary>
        /// Client details
        /// </summary>
        ProjectBuilderContextClient Client { get; }

        /// <summary>
        /// Project details
        /// </summary>
        ProjectBuilderContextProject Project { get; }

        /// <summary>
        ///  The success profile details.
        /// </summary>
        ProjectBuilderContextSuccessProfile SuccessProfile { get; }

        /// <summary>
        /// Content language for any translations
        /// </summary>
        Language Language { get; }
    }
}
